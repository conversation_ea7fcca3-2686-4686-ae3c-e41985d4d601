// WhatsApp Web Content Script - Enhanced version with better initialization, media handling, and file upload support
;(() => {
  console.log("WhatsApp Web Content Script v3.0 loading...")

  // Ensure we're on WhatsApp Web
  if (window.location.hostname !== "web.whatsapp.com") {
    console.log("Not on WhatsApp Web, content script exiting")
    return
  }

  // Flag to prevent multiple initializations
  if (window.whatsappAutomationLoaded) {
    console.log("Content script already loaded")
    return
  }
  window.whatsappAutomationLoaded = true

  console.log("WhatsApp Web Content Script v3.0 initialized")

  // Declare chrome variable
  const chrome = window.chrome

  // Listen for messages from popup
  chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log("Content script received:", request)

    if (request.action === "ping") {
      console.log("Content script responding to ping")
      sendResponse({ success: true, message: "Content script is ready" })
      return true
    }

    if (request.action === "sendMessage") {
      handleSendMessage(request.message, request.phoneNumber)
        .then((result) => {
          console.log("Message sent successfully:", result)
          sendResponse({ success: true, result })
        })
        .catch((error) => {
          console.error("Error sending message:", error)
          sendResponse({ success: false, error: error.message })
        })

      return true // Keep message channel open for async response
    }

    return false
  })

  // Send ready signal when content script loads
  function notifyReady() {
    try {
      chrome.runtime.sendMessage({ action: "contentScriptReady" })
    } catch (error) {
      console.log("Could not notify background script (popup may not be open)")
    }
  }

  // Wait for DOM to be ready before notifying
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", notifyReady)
  } else {
    notifyReady()
  }

  // Deserialize files from message passing format
  function deserializeFiles(serializedFiles) {
    console.log("\n=== DESERIALIZE FILES DEBUG ===")
    console.log("Serialized files received:", serializedFiles.length)

    const files = []

    for (let i = 0; i < serializedFiles.length; i++) {
      const serializedFile = serializedFiles[i]
      console.log(`\n--- Deserializing file ${i + 1} ---`)
      console.log("Serialized file:", {
        name: serializedFile.name,
        size: serializedFile.size,
        type: serializedFile.type,
        dataLength: serializedFile.data ? serializedFile.data.length : 0,
      })

      try {
        // Convert array back to Uint8Array
        const uint8Array = new Uint8Array(serializedFile.data)

        // Create File object
        const file = new File([uint8Array], serializedFile.name, {
          type: serializedFile.type,
          lastModified: serializedFile.lastModified,
        })

        console.log(`✅ File ${i + 1} deserialized successfully:`, {
          name: file.name,
          size: file.size,
          type: file.type,
          lastModified: file.lastModified,
          isFile: file instanceof File,
        })

        files.push(file)
      } catch (error) {
        console.error(`❌ Error deserializing file ${i + 1}:`, error)
      }
    }

    console.log("\n=== DESERIALIZATION SUMMARY ===")
    console.log("Input serialized files:", serializedFiles.length)
    console.log("Successfully deserialized:", files.length)

    return files
  }

  // Convert data files (from multi processing) to File objects
  async function convertDataFilesToFileObjects(dataFiles) {
    console.log("\n=== CONVERT DATA FILES TO FILE OBJECTS ===")
    console.log("Input data files:", dataFiles.length)

    const files = []

    for (let i = 0; i < dataFiles.length; i++) {
      const dataFile = dataFiles[i]
      console.log(`\n--- Converting data file ${i + 1} ---`)
      console.log("Data file:", {
        name: dataFile.name,
        type: dataFile.type,
        size: dataFile.size,
        hasData: !!dataFile.data
      })

      try {
        if (!dataFile.data) {
          console.warn(`❌ Data file ${i + 1}: No data available`)
          continue
        }

        // Convert base64 data to File object
        const base64Data = dataFile.data.split(",")[1]
        if (!base64Data) {
          console.warn(`❌ Data file ${i + 1}: Invalid base64 data format`)
          continue
        }

        const byteCharacters = atob(base64Data)
        const byteNumbers = new Array(byteCharacters.length)
        for (let j = 0; j < byteCharacters.length; j++) {
          byteNumbers[j] = byteCharacters.charCodeAt(j)
        }
        const byteArray = new Uint8Array(byteNumbers)

        const file = new File([byteArray], dataFile.name, {
          type: dataFile.type,
          lastModified: dataFile.lastModified,
        })

        console.log(`✅ Data file ${i + 1}: Successfully converted to File object:`, {
          name: file.name,
          size: file.size,
          type: file.type,
          lastModified: file.lastModified,
        })

        files.push(file)
      } catch (error) {
        console.error(`❌ Error converting data file ${i + 1}:`, dataFile.name, error)
      }
    }

    console.log("\n=== DATA FILE CONVERSION SUMMARY ===")
    console.log("Input data files:", dataFiles.length)
    console.log("Successfully converted:", files.length)

    return files
  }

  async function handleSendMessage(messageData, phoneNumber) {
    try {
      console.log("\n=== CONTENT SCRIPT SEND MESSAGE DEBUG ===")
      console.log(`Starting message send process for ${phoneNumber}`)
      console.log("Message data received:", {
        ...messageData,
        files: messageData.files ? `${messageData.files.length} files` : null,
        message: messageData.message ? `"${messageData.message.substring(0, 50)}..."` : "no message",
        caption: messageData.caption ? `"${messageData.caption.substring(0, 50)}..."` : "no caption",
        link: messageData.link ? `"${messageData.link.substring(0, 50)}..."` : "no link",
      })

      // Use the enhanced loading function
      await waitForWhatsAppToFullyLoad()
      await waitForChatToLoad()

      // Add a small delay to ensure everything is ready
      await sleep(2000)

      // Check if we have files to send
      if (messageData.files && messageData.files.length > 0) {
        console.log(`\n=== FILE SENDING WITH API CONTENT DEBUG ===`)
        console.log(`Processing ${messageData.files.length} files...`)
        console.log("API message content:", {
          message: messageData.message || "none",
          caption: messageData.caption || "none",
          link: messageData.link || "none",
          multiType: messageData.multiType || "none",
          isMultiItem: messageData.isMultiItem || false
        })

        // Handle files based on their format
        let files = []
        if (messageData.files[0] && messageData.files[0].data && typeof messageData.files[0].data === 'string') {
          // Files are in serialized format (from message passing)
          files = deserializeFiles(messageData.files)
          console.log(`Deserialized ${files.length} files successfully`)
        } else {
          // Files are already in our internal format (from multi processing)
          files = await convertDataFilesToFileObjects(messageData.files)
          console.log(`Converted ${files.length} data files to File objects`)
        }

        // Determine what text content to use
        let primaryText = ""
        let secondaryText = ""

        // Priority: caption > message > link
        if (messageData.caption && messageData.caption.trim()) {
          primaryText = messageData.caption.trim()
          if (messageData.message && messageData.message.trim() && messageData.message.trim() !== primaryText) {
            secondaryText = messageData.message.trim()
          }
        } else if (messageData.message && messageData.message.trim()) {
          primaryText = messageData.message.trim()
        }

        // Add link to secondary text if available
        if (messageData.link && messageData.link.trim()) {
          const linkText = `🔗 ${messageData.link.trim()}`
          if (secondaryText) {
            secondaryText += `\n\n${linkText}`
          } else if (!primaryText) {
            primaryText = linkText
          } else {
            secondaryText = linkText
          }
        }

        console.log("Text content determined:", {
          primaryText: primaryText ? `"${primaryText.substring(0, 50)}..."` : "none",
          secondaryText: secondaryText ? `"${secondaryText.substring(0, 50)}..."` : "none",
        })

        // Send files with captions
        for (let i = 0; i < files.length; i++) {
          const file = files[i]
          console.log(`\n--- Processing file ${i + 1}/${files.length} ---`)

          // Validate file before processing
          if (!file || !file.name || file.size === undefined) {
            console.error(`❌ Invalid file at index ${i}:`, file)
            const errorMessage = `❌ Failed to send file ${i + 1}: Invalid file data\n\nFile details:\n- Name: ${file?.name || "undefined"}\n- Size: ${file?.size || "undefined"}\n- Type: ${file?.type || "undefined"}\n\n${primaryText || ""}`
            await sendTextMessage(errorMessage)
            continue
          }

          console.log(`✅ File ${i + 1} validation passed: ${file.name} (${formatFileSize(file.size)})`)

          try {
            // For multi-file messages, use the full caption for ALL files
            // For single file messages, use caption for first file only
            let caption = ""

            if (messageData.isMultiItem && primaryText) {
              // Multi-file message: use full combined caption for all files
              caption = primaryText
              console.log(`Multi-file message: Using full caption for file ${i + 1}`)
            } else if (i === 0 && primaryText) {
              // Single file or first file: use primary text as caption
              caption = primaryText
              console.log(`Single file or first file: Using caption for file ${i + 1}`)
            } else {
              console.log(`File ${i + 1}: No caption (not first file in single-file message)`)
            }

            console.log(
              `Sending file ${i + 1} with caption:`,
              caption ? `"${caption.substring(0, 100)}..."` : "no caption",
            )

            await sendFileWithCaption(file, caption)
            console.log(`✅ File ${i + 1} sent successfully with caption: ${caption ? 'YES' : 'NO'}`)

            // Wait between files
            if (i < files.length - 1) {
              console.log(`Waiting 3 seconds before next file...`)
              await sleep(3000)
            }
          } catch (fileError) {
            console.error(`❌ Error sending file ${file.name}:`, fileError)

            // If file sending fails, try to send as text message with file info
            const fallbackMessage = `❌ Failed to send file: ${file.name} (${formatFileSize(file.size)})\nError: ${fileError.message}\n\n${primaryText || ""}`
            await sendTextMessage(fallbackMessage)
          }
        }

        // Send secondary text content if available (after all files)
        if (secondaryText) {
          console.log("Sending secondary text content after files...")
          await sleep(2000)
          await sendTextMessage(secondaryText)
        }

        console.log("✅ Files and API content sent successfully")
        return { success: true, phoneNumber, messageData, method: "files_with_api_content" }
      } else if (messageData.link && messageData.link.trim() && !messageData.isMultiItem) {
        // Only process main link if this is NOT a multi-item message
        // Multi-item messages should only use files from the multi field
        console.log("\n=== SINGLE MEDIA LINK SENDING DEBUG ===")
        console.log("Attempting to send single media with caption (not from multi field)...")
        const isImageAccessible = await checkImageAccessibility(messageData.link)

        if (isImageAccessible) {
          try {
            // Use caption if available, otherwise use message
            const captionText =
              messageData.caption && messageData.caption.trim()
                ? messageData.caption.trim()
                : messageData.message && messageData.message.trim()
                  ? messageData.message.trim()
                  : ""

            await sendMediaWithCaption(messageData.link, captionText)

            // Send additional message if both caption and message exist and are different
            if (
              messageData.message &&
              messageData.message.trim() &&
              messageData.caption &&
              messageData.caption.trim() &&
              messageData.message.trim() !== messageData.caption.trim()
            ) {
              await sleep(2000)
              await sendTextMessage(messageData.message.trim())
            }

            console.log("✅ Single media sent successfully")
            return { success: true, phoneNumber, messageData, method: "single_media" }
          } catch (mediaError) {
            console.log("❌ Media upload failed, falling back to formatted text:", mediaError.message)
            await sendFormattedFallbackMessage(messageData)
          }
        } else {
          console.log("❌ Image not accessible, sending formatted text message")
          await sendFormattedFallbackMessage(messageData)
        }
      } else {
        // Send text content only
        console.log("\n=== TEXT MESSAGE SENDING DEBUG ===")

        // Log if we're skipping main link because it's a multi-item
        if (messageData.link && messageData.link.trim() && messageData.isMultiItem) {
          console.log("🚫 Skipping main link field because this is a multi-item message")
          console.log("✅ Multi-item messages should only use files from the multi field")
        }

        let textToSend = ""

        // Priority: caption > message
        if (messageData.caption && messageData.caption.trim()) {
          textToSend = messageData.caption.trim()

          // Add message if different from caption
          if (messageData.message && messageData.message.trim() && messageData.message.trim() !== textToSend) {
            textToSend += `\n\n${messageData.message.trim()}`
          }
        } else if (messageData.message && messageData.message.trim()) {
          textToSend = messageData.message.trim()
        }

        // Add link if available
        if (messageData.link && messageData.link.trim()) {
          if (textToSend) {
            textToSend += `\n\n🔗 ${messageData.link.trim()}`
          } else {
            textToSend = `🔗 ${messageData.link.trim()}`
          }
        }

        if (textToSend) {
          console.log("Sending combined text message...")
          await sendTextMessage(textToSend)
        } else {
          console.log("⚠️ No content to send")
        }
      }

      console.log("✅ Message sending process completed successfully")
      return { success: true, phoneNumber, messageData, method: "api_content" }
    } catch (error) {
      console.error("❌ Error in handleSendMessage:", error)
      console.error("Error details:", {
        message: error.message,
        stack: error.stack,
        phoneNumber: phoneNumber,
        messageData: messageData,
      })
      throw error
    }
  }

  async function sendFileWithCaption(file, caption) {
    try {
      console.log("\n=== SEND FILE WITH CAPTION DEBUG ===")

      // Validate file object
      if (!file) {
        throw new Error("File object is null or undefined")
      }

      if (!file.name) {
        throw new Error("File name is missing")
      }

      if (file.size === undefined || file.size === null || isNaN(file.size)) {
        throw new Error(`File size is invalid: ${file.size}`)
      }

      console.log("✅ File validation passed")
      console.log("File details:", {
        name: file.name,
        type: file.type || "unknown",
        size: file.size,
        caption: caption || "no caption",
        isFile: file instanceof File,
        constructor: file.constructor.name,
      })

      // Find the attachment button
      console.log("🔍 Looking for attachment button...")
      let attachButton = await waitForAttachButton()

      if (!attachButton) {
        console.log("⚠️ Attachment button not found, trying keyboard shortcut...")
        // Try keyboard shortcut as fallback
        const messageInput = document.querySelector('[data-testid="conversation-compose-box-input"], div[contenteditable="true"]')
        if (messageInput) {
          messageInput.focus()
          // Simulate Ctrl+Shift+A (common attachment shortcut)
          const event = new KeyboardEvent('keydown', {
            key: 'a',
            code: 'KeyA',
            ctrlKey: true,
            shiftKey: true,
            bubbles: true
          })
          messageInput.dispatchEvent(event)
          await sleep(2000)

          // Try to find attachment button again after keyboard shortcut
          attachButton = await waitForAttachButton()
        }

        if (!attachButton) {
          throw new Error("Attachment button not found even after keyboard shortcut")
        }
      }

      console.log("✅ Attachment button found")
      attachButton.click()
      await sleep(2000) // Increased wait time

      // Determine file type and click appropriate option
      let optionButton
      console.log("Determining file type for upload option...")
      if (file.type && (file.type.startsWith("image/") || file.type.startsWith("video/"))) {
        console.log("File is image/video, looking for photo button...")
        optionButton = await waitForPhotoButton()
      } else {
        console.log("File is document, looking for document button...")
        optionButton = await waitForDocumentButton()

        // Fallback: if document button not found, try to find any file input
        if (!optionButton) {
          console.log("Document button not found, trying to find any file input...")
          const fileInputs = document.querySelectorAll('input[type="file"]')
          for (const input of fileInputs) {
            if (input.offsetParent !== null || input.style.display !== "none") {
              console.log("Found file input as fallback")
              optionButton = input
              break
            }
          }
        }
      }

      if (!optionButton) {
        throw new Error("File type option button not found")
      }
      console.log("✅ File type option button found")

      // Click the option button (unless it's a file input)
      if (optionButton.tagName.toLowerCase() !== "input") {
        optionButton.click()
        await sleep(2000) // Increased wait time
      }

      // Upload the file
      console.log("Uploading file...")
      await uploadFile(file)
      console.log("✅ File uploaded")

      // Wait for file to process
      console.log("Waiting for file to process...")
      await sleep(5000) // Increased wait time

      // Add caption if provided
      if (caption && caption.trim()) {
        console.log("Adding caption...")
        const captionBox = await waitForCaptionBox()
        if (captionBox) {
          await insertText(captionBox, caption)
          await sleep(1000)
          console.log("✅ Caption added")
        } else {
          console.log("⚠️ Caption box not found, proceeding without caption")
        }
      }

      // Send the file
      console.log("Looking for send button...")
      const sendButton = await waitForMediaSendButton()
      if (!sendButton) {
        console.log("⚠️ Send button not found, trying coordinate-based click...")

        // Try clicking at typical send button coordinates
        const success = await clickAtCoordinates(window.innerWidth - 60, window.innerHeight - 60)
        if (!success) {
          throw new Error("Send button not found and coordinate click failed")
        }
      } else {
        console.log("✅ Send button found, clicking...")
        sendButton.click()
      }

      await sleep(8000) // Increased wait time for file to send completely

      console.log("✅ File sent successfully:", file.name)
    } catch (error) {
      console.error("❌ Error sending file:", error)
      console.error("Error context:", {
        fileName: file?.name || "unknown",
        fileSize: file?.size || "unknown",
        fileType: file?.type || "unknown",
        caption: caption || "none",
        errorMessage: error.message,
        errorStack: error.stack,
      })
      throw new Error(`Failed to send file "${file?.name || "unknown"}": ${error.message}`)
    }
  }

  async function uploadFile(file) {
    try {
      console.log("Uploading file:", file.name, file.type, file.size)

      // Find file input
      const fileInput = document.querySelector('input[type="file"]')

      if (!fileInput) {
        throw new Error("File input not found")
      }

      // Create FileList-like object
      const dataTransfer = new DataTransfer()
      dataTransfer.items.add(file)
      fileInput.files = dataTransfer.files

      // Trigger change event
      const changeEvent = new Event("change", { bubbles: true })
      fileInput.dispatchEvent(changeEvent)

      console.log("File uploaded successfully")
    } catch (error) {
      console.error("Error uploading file:", error)
      throw error
    }
  }

  async function waitForDocumentButton() {
    console.log("Looking for Document button...")
    return new Promise((resolve) => {
      let attempts = 0
      const maxAttempts = 15 // Increased attempts

      const findButton = () => {
        attempts++

        // More comprehensive selectors for document button
        const selectors = [
          // Primary selectors (most common)
          'li[data-testid="mi-attach-document"]',
          'div[data-testid="mi-attach-document"]',
          'button[data-testid="mi-attach-document"]',
          'span[data-testid="mi-attach-document"]',

          // Alternative selectors with different attributes
          'button[aria-label*="Document"]',
          'button[aria-label*="document"]',
          'li[aria-label*="Document"]',
          'li[aria-label*="document"]',
          'span[title*="Document"]',
          'div[title*="Document"]',
          'li[title*="Document"]',

          // Icon-based selectors
          'span[data-icon="document"]',
          'div[data-icon="document"]',
          'li[data-icon="document"]',
          'span[data-icon="attach-document"]',
          'div[data-icon="attach-document"]',

          // Menu item selectors (documents are usually second in menu)
          "ul li:nth-child(2)",
          'div[role="menu"] > div:nth-child(2)',
          'div[role="menu"] li:nth-child(2)',
          'ul[role="menu"] li:nth-child(2)',

          // Generic menu item selectors
          'li[role="button"]',
          'div[role="button"]',
          'span[role="button"]',

          // Fallback selectors based on common patterns
          "div[data-tab] li:nth-child(2)",
          "ul > li:nth-child(2)",
        ]

        for (const selector of selectors) {
          try {
            const elements = document.querySelectorAll(selector)

            for (const element of elements) {
              // Check if element is visible and clickable
              if (
                element &&
                element.offsetParent !== null &&
                element.getBoundingClientRect().width > 0 &&
                element.getBoundingClientRect().height > 0
              ) {
                // Additional checks for document button
                const elementText =
                  element.textContent || element.getAttribute("aria-label") || element.getAttribute("title") || ""

                const hasDocumentKeyword =
                  elementText.toLowerCase().includes("document") ||
                  elementText.toLowerCase().includes("file") ||
                  elementText.toLowerCase().includes("pdf") ||
                  element.querySelector("svg") || // Many buttons have SVG icons
                  element.querySelector('[data-icon*="document"]')

                // Check if it's the second item in a menu (documents are usually second after photos)
                const isSecondInMenu =
                  element.matches("li:nth-child(2), div:nth-child(2)") ||
                  (element.previousElementSibling && !element.previousElementSibling.previousElementSibling)

                // Check for file input association
                const hasFileInput =
                  element.querySelector('input[type="file"]') ||
                  element.onclick?.toString().includes("file") ||
                  element.getAttribute("onclick")?.includes("file")

                if (hasDocumentKeyword || isSecondInMenu || hasFileInput) {
                  console.log("Document button found with selector:", selector)
                  console.log("Element details:", {
                    tagName: element.tagName,
                    className: element.className,
                    ariaLabel: element.getAttribute("aria-label"),
                    title: element.getAttribute("title"),
                    testId: element.getAttribute("data-testid"),
                    textContent: element.textContent?.substring(0, 50),
                    hasDocumentKeyword,
                    isSecondInMenu,
                    hasFileInput,
                  })
                  resolve(element)
                  return
                }
              }
            }
          } catch (error) {
            console.log(`Error checking selector ${selector}:`, error.message)
            continue
          }
        }

        // Alternative approach: look for file inputs that accept documents
        if (attempts > 5) {
          console.log("Trying alternative approach - looking for document file inputs...")
          try {
            const fileInputs = document.querySelectorAll('input[type="file"]')
            for (const input of fileInputs) {
              if (
                input.accept &&
                (input.accept.includes("pdf") ||
                  input.accept.includes("document") ||
                  input.accept.includes("application") ||
                  (!input.accept.includes("image") && !input.accept.includes("video")))
              ) {
                console.log("Found document file input directly")
                resolve(input)
                return
              }
            }
          } catch (error) {
            console.log("File input search failed:", error.message)
          }
        }

        // Last resort: try to find any clickable element in the attachment menu
        if (attempts > 10) {
          console.log("Last resort - looking for any menu items...")
          try {
            const menuItems = document.querySelectorAll('ul li, div[role="menu"] > div, div[role="menu"] li')
            for (let i = 0; i < menuItems.length; i++) {
              const item = menuItems[i]
              if (item && item.offsetParent !== null && i > 0) {
                // Skip first item (usually photos)
                console.log(`Trying menu item ${i}:`, {
                  tagName: item.tagName,
                  textContent: item.textContent?.substring(0, 30),
                  className: item.className,
                })
                resolve(item)
                return
              }
            }
          } catch (error) {
            console.log("Menu item search failed:", error.message)
          }
        }

        if (attempts >= maxAttempts) {
          console.log("Document button not found after", maxAttempts, "attempts")

          // Debug: Log all available menu items
          console.log("Available menu items for debugging:")
          try {
            const allMenuItems = document.querySelectorAll(
              'ul li, div[role="menu"] > div, div[role="menu"] li, li[role="button"]',
            )
            allMenuItems.forEach((item, index) => {
              if (item.offsetParent !== null) {
                console.log(`Menu item ${index}:`, {
                  tagName: item.tagName,
                  className: item.className,
                  ariaLabel: item.getAttribute("aria-label"),
                  title: item.getAttribute("title"),
                  testId: item.getAttribute("data-testid"),
                  textContent: item.textContent?.substring(0, 50),
                  innerHTML: item.innerHTML.substring(0, 100),
                })
              }
            })
          } catch (debugError) {
            console.log("Debug logging failed:", debugError.message)
          }

          resolve(null)
        } else {
          setTimeout(findButton, 1000)
        }
      }

      findButton()
    })
  }

  // Check if image URL is accessible
  async function checkImageAccessibility(imageUrl) {
    try {
      console.log("Checking image accessibility:", imageUrl)
      return true // Assume accessible and let the actual upload handle errors
    } catch (error) {
      console.log("Image accessibility check failed:", error.message)
      return false
    }
  }

  async function sendFormattedFallbackMessage(messageData) {
    try {
      let formattedMessage = ""

      // Priority: caption > message > link
      if (messageData.caption && messageData.caption.trim()) {
        formattedMessage += messageData.caption.trim()
      }

      if (
        messageData.message &&
        messageData.message.trim() &&
        messageData.message.trim() !== messageData.caption?.trim()
      ) {
        if (formattedMessage) {
          formattedMessage += "\n\n"
        }
        formattedMessage += messageData.message.trim()
      }

      if (messageData.link && messageData.link.trim()) {
        if (formattedMessage) {
          formattedMessage += "\n\n🔗 "
        } else {
          formattedMessage += "🔗 "
        }
        formattedMessage += messageData.link.trim()
      }

      if (formattedMessage.trim()) {
        console.log("Sending formatted fallback message:", formattedMessage.substring(0, 100) + "...")
        await sendTextMessage(formattedMessage)
      } else {
        console.log("⚠️ No content available for fallback message")
      }
    } catch (error) {
      console.error("Error sending formatted fallback message:", error)
      throw error
    }
  }

  async function sendMediaWithCaption(mediaUrl, caption) {
    try {
      console.log("Sending media:", mediaUrl, "with caption:", caption)

      const attachButton = await waitForAttachButton()
      if (!attachButton) {
        throw new Error("Attachment button not found")
      }

      attachButton.click()
      await sleep(1500)

      const photoVideoButton = await waitForPhotoButton()
      if (!photoVideoButton) {
        throw new Error("Photos & Videos button not found")
      }

      photoVideoButton.click()
      await sleep(1500)

      await uploadMediaFromUrl(mediaUrl)
      await sleep(4000)

      if (caption && caption.trim()) {
        const captionBox = await waitForCaptionBox()
        if (captionBox) {
          await insertText(captionBox, caption)
          await sleep(1000)
        }
      }

      const mediaSendButton = await waitForMediaSendButton()
      if (!mediaSendButton) {
        throw new Error("Media send button not found")
      }

      mediaSendButton.click()
      await sleep(3000)

      console.log("Media sent successfully")
    } catch (error) {
      console.error("Error sending media:", error)
      throw error
    }
  }

  async function sendTextMessage(message) {
    try {
      console.log("Sending text message:", message.substring(0, 50) + "...")

      const messageBox = await waitForMessageBox()
      if (!messageBox) {
        throw new Error("Message input box not found")
      }

      console.log("Message box found, inserting text...")
      await insertFormattedText(messageBox, message)
      await sleep(2000)

      const sendButton = await waitForSendButton()
      if (!sendButton) {
        throw new Error("Send button not found")
      }

      console.log("Send button found, clicking...")
      sendButton.click()
      await sleep(2000)

      console.log("Text message sent successfully")
    } catch (error) {
      console.error("Error sending text message:", error)
      throw error
    }
  }

  async function insertFormattedText(element, text) {
    try {
      console.log("Inserting formatted text with improved method...")
      console.log("Original text first 100 chars:", text.substring(0, 100))

      // Focus the element first
      element.focus()
      element.click()
      await sleep(200)

      // Clear existing content completely
      element.innerHTML = ""
      element.textContent = ""
      await sleep(200)

      // Method 1: Direct clipboard approach (most reliable)
      try {
        console.log("Trying clipboard method...")

        // Write to clipboard
        await navigator.clipboard.writeText(text)
        await sleep(100)

        // Focus element again
        element.focus()
        element.click()
        await sleep(100)

        // Paste from clipboard
        const pasteSuccess = document.execCommand("paste")
        await sleep(1000)

        // Verify the paste worked
        const pastedText = element.textContent || element.innerText || ""
        console.log("Clipboard paste result - length:", pastedText.length)
        console.log("Clipboard paste result - first 100 chars:", pastedText.substring(0, 100))

        if (pastedText.length >= text.length * 0.9 && pastedText.includes(text.substring(0, 50))) {
          console.log("Clipboard paste successful!")
          return
        }
      } catch (clipboardError) {
        console.log("Clipboard method failed:", clipboardError)
      }

      // Method 2: Line-by-line insertion with proper line breaks
      try {
        console.log("Trying line-by-line insertion...")

        // Clear again
        element.innerHTML = ""
        element.textContent = ""
        element.focus()
        await sleep(200)

        const lines = text.split("\n")
        console.log("Inserting", lines.length, "lines...")

        for (let i = 0; i < lines.length; i++) {
          const line = lines[i]
          console.log(`Inserting line ${i + 1}: "${line.substring(0, 30)}..."`)

          // Insert the line text
          if (line.length > 0) {
            document.execCommand("insertText", false, line)
          }

          // Add line break if not the last line
          if (i < lines.length - 1) {
            // Create and dispatch Shift+Enter event
            const enterEvent = new KeyboardEvent("keydown", {
              key: "Enter",
              code: "Enter",
              keyCode: 13,
              which: 13,
              shiftKey: true,
              bubbles: true,
              cancelable: true,
            })
            element.dispatchEvent(enterEvent)

            // Also try the keyup event
            const enterUpEvent = new KeyboardEvent("keyup", {
              key: "Enter",
              code: "Enter",
              keyCode: 13,
              which: 13,
              shiftKey: true,
              bubbles: true,
              cancelable: true,
            })
            element.dispatchEvent(enterUpEvent)

            await sleep(50)
          }
        }

        await sleep(1000)

        // Verify line-by-line insertion
        const lineInsertedText = element.textContent || element.innerText || ""
        console.log("Line-by-line result - length:", lineInsertedText.length)
        console.log("Line-by-line result - first 100 chars:", lineInsertedText.substring(0, 100))

        if (lineInsertedText.length >= text.length * 0.9 && lineInsertedText.includes(text.substring(0, 50))) {
          console.log("Line-by-line insertion successful!")
          return
        }
      } catch (lineError) {
        console.log("Line-by-line method failed:", lineError)
      }

      // Method 3: innerHTML with proper BR tags
      try {
        console.log("Trying innerHTML method...")

        element.innerHTML = ""
        element.focus()
        await sleep(200)

        // Convert line breaks to BR tags
        const htmlText = text.replace(/\n/g, "<br>")
        element.innerHTML = htmlText

        // Trigger input events
        const inputEvent = new Event("input", { bubbles: true, cancelable: true })
        element.dispatchEvent(inputEvent)

        const changeEvent = new Event("change", { bubbles: true, cancelable: true })
        element.dispatchEvent(changeEvent)

        await sleep(1000)

        // Verify innerHTML method
        const htmlInsertedText = element.textContent || element.innerText || ""
        console.log("innerHTML result - length:", htmlInsertedText.length)
        console.log("innerHTML result - first 100 chars:", htmlInsertedText.substring(0, 100))

        if (htmlInsertedText.length >= text.length * 0.9 && htmlInsertedText.includes(text.substring(0, 50))) {
          console.log("innerHTML insertion successful!")
          return
        }
      } catch (htmlError) {
        console.log("innerHTML method failed:", htmlError)
      }

      // Method 4: Character-by-character with better line break handling
      try {
        console.log("Trying character-by-character method...")

        element.innerHTML = ""
        element.focus()
        await sleep(200)

        let charCount = 0
        for (const char of text) {
          if (char === "\n") {
            // Insert line break using Shift+Enter
            const event = new KeyboardEvent("keydown", {
              key: "Enter",
              code: "Enter",
              keyCode: 13,
              which: 13,
              shiftKey: true,
              bubbles: true,
              cancelable: true,
            })
            element.dispatchEvent(event)
          } else {
            document.execCommand("insertText", false, char)
          }

          charCount++

          // Small delay every 100 characters to prevent freezing
          if (charCount % 100 === 0) {
            await sleep(10)
          }
        }

        await sleep(1000)

        // Verify character-by-character insertion
        const charInsertedText = element.textContent || element.innerText || ""
        console.log("Character-by-character result - length:", charInsertedText.length)
        console.log("Character-by-character result - first 100 chars:", charInsertedText.substring(0, 100))

        if (charInsertedText.length >= text.length * 0.8) {
          console.log("Character-by-character insertion completed!")
          return
        }
      } catch (charError) {
        console.log("Character-by-character method failed:", charError)
      }

      // If all methods failed, throw an error
      throw new Error("All text insertion methods failed to preserve complete message")
    } catch (error) {
      console.error("Error in insertFormattedText:", error)
      throw error
    }
  }

  // Improved insertText function for contenteditable elements
  async function insertText(element, text) {
    try {
      console.log("Inserting text into element:", text.substring(0, 50) + "...")

      // Clear existing content first
      element.innerHTML = ""
      element.textContent = ""

      // Focus the element
      element.focus()
      await sleep(100)

      // Try multiple methods to ensure text is inserted

      // Method 1: Direct content setting
      element.textContent = text

      // Method 2: innerHTML for contenteditable
      if (element.contentEditable === "true") {
        element.innerHTML = text.replace(/\n/g, '<br>')
      }

      // Method 3: Simulate typing for better compatibility
      element.dispatchEvent(new Event('focus', { bubbles: true }))
      await sleep(50)

      // Method 4: Use document.execCommand if available
      if (document.execCommand) {
        try {
          document.execCommand('selectAll', false, null)
          document.execCommand('insertText', false, text)
        } catch (e) {
          console.log("execCommand not supported, using fallback")
        }
      }

      // Trigger events to notify WhatsApp
      const events = ['input', 'change', 'keyup', 'paste']
      for (const eventType of events) {
        const event = new Event(eventType, { bubbles: true, cancelable: true })
        element.dispatchEvent(event)
        await sleep(10)
      }

      // Final verification
      await sleep(200)
      const finalContent = element.textContent || element.innerText || ""
      console.log("Text insertion result:", finalContent.substring(0, 50) + "...")

      if (!finalContent.includes(text.substring(0, 20))) {
        console.warn("Text insertion may have failed, trying alternative method...")

        // Alternative method: character by character
        element.focus()
        for (let i = 0; i < text.length; i++) {
          const char = text[i]
          const keyEvent = new KeyboardEvent('keydown', {
            key: char,
            char: char,
            bubbles: true,
            cancelable: true
          })
          element.dispatchEvent(keyEvent)

          if (char === '\n') {
            element.innerHTML += '<br>'
          } else {
            element.textContent += char
          }

          const inputEvent = new Event('input', { bubbles: true })
          element.dispatchEvent(inputEvent)

          if (i % 10 === 0) await sleep(10) // Small delay every 10 characters
        }
      }

      console.log("✅ Text insertion completed")

    } catch (error) {
      console.error("Error inserting text:", error)
      throw error
    }
  }

  async function waitForWhatsAppToLoad() {
    console.log("Waiting for WhatsApp to load...")
    return new Promise((resolve) => {
      let attempts = 0
      const maxAttempts = 15

      const checkLoaded = () => {
        attempts++

        // Check if WhatsApp Web is fully loaded
        if (document.querySelector('[data-testid="side"]') || document.querySelector("#side")) {
          console.log("WhatsApp Web loaded")
          resolve()
          return
        }

        if (attempts >= maxAttempts) {
          console.log("WhatsApp load timeout, proceeding anyway...")
          resolve()
        } else {
          setTimeout(checkLoaded, 1000)
        }
      }

      checkLoaded()
    })
  }

  async function waitForChatToLoad() {
    console.log("Waiting for chat to load...")
    return new Promise((resolve) => {
      let attempts = 0
      const maxAttempts = 15

      const checkLoaded = () => {
        attempts++

        // Check if chat is loaded
        const chatLoaded =
          document.querySelector('[data-testid="main"]') ||
          document.querySelector('[data-testid="conversation-panel-body"]') ||
          document.querySelector('div[data-tab="6"]')

        if (chatLoaded) {
          console.log("Chat loaded")
          resolve()
          return
        }

        if (attempts >= maxAttempts) {
          console.log("Chat load timeout, proceeding anyway...")
          resolve()
        } else {
          setTimeout(checkLoaded, 1000)
        }
      }

      checkLoaded()
    })
  }

  async function waitForAttachButton() {
    console.log("🔍 Looking for attachment button...")
    console.log("📱 Current URL:", window.location.href)
    console.log("📱 Page title:", document.title)

    return new Promise((resolve) => {
      let attempts = 0
      const maxAttempts = 20 // Increased attempts

      const findButton = () => {
        attempts++
        console.log(`🔍 Attempt ${attempts}/${maxAttempts} to find attachment button...`)

        // More comprehensive selectors for the attachment button (Updated 2024)
        const selectors = [
          // Primary selectors (most common - 2024 WhatsApp)
          '[data-testid="clip"]',
          '[data-testid="attach-menu-plus"]',
          '[data-testid="plus"]',
          'span[data-testid="clip"]',
          'button[data-testid="clip"]',
          'div[data-testid="clip"]',

          // New 2024 selectors
          '[aria-label="Attach"]',
          '[aria-label="Attach file"]',
          '[title="Attach"]',
          '[title="Attach file"]',
          'button[aria-label*="Attach"]',
          'button[aria-label*="attach"]',
          'div[title*="Attach"]',
          'div[title*="attach"]',

          // Icon-based selectors
          'span[data-icon="clip"]',
          'div[data-icon="clip"]',
          'span[data-icon="plus"]',
          'div[data-icon="plus"]',
          'span[data-icon="attach"]',

          // More generic selectors
          'button[title*="Attach"]',
          'span[title*="Attach"]',
          '[role="button"][aria-label*="Attach"]',
          '[role="button"][title*="Attach"]',

          // Fallback selectors based on common patterns
          'div[data-tab="10"] button[type="button"]',
          'footer button[type="button"]',
          "div[contenteditable] + div button",
          'div[data-testid="conversation-compose-box-input"] + div button',

          // SVG-based selectors (WhatsApp often uses SVG icons)
          'button svg[viewBox*="24"]',
          'span svg[viewBox*="24"]',
          'div svg[viewBox*="24"]',

          // Specific SVG path selectors for attachment icon
          'button svg path[d*="M1.5 6"]', // Common attachment icon path
          'span svg path[d*="M1.5 6"]',
        ]

        for (const selector of selectors) {
          try {
            const elements = document.querySelectorAll(selector)

            for (const element of elements) {
              // Check if element is visible and clickable
              if (
                element &&
                element.offsetParent !== null &&
                element.getBoundingClientRect().width > 0 &&
                element.getBoundingClientRect().height > 0
              ) {
                // Additional checks for attachment button
                const elementText =
                  element.textContent || element.getAttribute("aria-label") || element.getAttribute("title") || ""
                const hasAttachKeyword =
                  elementText.toLowerCase().includes("attach") ||
                  elementText.toLowerCase().includes("clip") ||
                  element.querySelector("svg") // Many attachment buttons have SVG icons

                // Check if it's in the message input area (bottom of screen)
                const rect = element.getBoundingClientRect()
                const isInMessageArea = rect.top > window.innerHeight * 0.5

                if (hasAttachKeyword || isInMessageArea) {
                  console.log("Attachment button found with selector:", selector)
                  console.log("Element details:", {
                    tagName: element.tagName,
                    className: element.className,
                    ariaLabel: element.getAttribute("aria-label"),
                    title: element.getAttribute("title"),
                    testId: element.getAttribute("data-testid"),
                  })
                  resolve(element)
                  return
                }
              }
            }
          } catch (error) {
            console.log(`Error checking selector ${selector}:`, error.message)
            continue
          }
        }

        // If we can't find the attachment button, try to find it by looking for the message input area
        if (attempts > 5) {
          console.log("Trying alternative approach - looking near message input...")
          try {
            const messageBox = document.querySelector(
              '[data-testid="conversation-compose-box-input"], div[title="Type a message"], div[contenteditable="true"]',
            )
            if (messageBox) {
              const messageContainer = messageBox.closest('footer, div[data-tab="10"], .copyable-area')
              if (messageContainer) {
                const buttonsInContainer = messageContainer.querySelectorAll(
                  'button, span[role="button"], div[role="button"]',
                )

                for (const button of buttonsInContainer) {
                  if (
                    button.offsetParent !== null &&
                    button.getBoundingClientRect().width > 0 &&
                    (button.querySelector("svg") || button.textContent.includes("📎"))
                  ) {
                    console.log("Found attachment button near message input")
                    resolve(button)
                    return
                  }
                }
              }
            }
          } catch (error) {
            console.log("Alternative approach failed:", error.message)
          }
        }

        if (attempts >= maxAttempts) {
          console.log("❌ Attachment button not found after", maxAttempts, "attempts")
          console.log("🔍 Available buttons in message area:")

          // Debug: Log all buttons in the message area for troubleshooting
          try {
            const allButtons = document.querySelectorAll(
              'footer button, div[data-tab="10"] button, .copyable-area button, [role="button"], button, span[role="button"]',
            )
            console.log(`📋 Found ${allButtons.length} clickable elements:`)
            allButtons.forEach((btn, index) => {
              if (btn.offsetParent !== null) {
                const rect = btn.getBoundingClientRect()
                console.log(`Button ${index}:`, {
                  tagName: btn.tagName,
                  className: btn.className,
                  ariaLabel: btn.getAttribute("aria-label"),
                  title: btn.getAttribute("title"),
                  testId: btn.getAttribute("data-testid"),
                  dataIcon: btn.getAttribute("data-icon"),
                  innerHTML: btn.innerHTML.substring(0, 100),
                  position: `${Math.round(rect.top)}, ${Math.round(rect.left)}`,
                  size: `${Math.round(rect.width)}x${Math.round(rect.height)}`
                })
              }
            })

            // Try to find any element that might be the attachment button as last resort
            console.log("🔍 Trying last resort - looking for any element with clip/attach/plus...")
            const lastResortSelectors = [
              '*[class*="clip"]', '*[class*="attach"]', '*[class*="plus"]',
              '*[aria-label*="clip"]', '*[aria-label*="attach"]', '*[title*="clip"]', '*[title*="attach"]',
              'svg[viewBox*="24"]', 'button svg', 'span svg'
            ]

            for (const selector of lastResortSelectors) {
              const elements = document.querySelectorAll(selector)
              for (const elem of elements) {
                if (elem.offsetParent !== null && elem.getBoundingClientRect().width > 0) {
                  const parent = elem.closest('button, span[role="button"], div[role="button"]')
                  if (parent) {
                    console.log("🎯 Found potential attachment button via last resort:", parent)
                    resolve(parent)
                    return
                  }
                }
              }
            }

          } catch (debugError) {
            console.log("Debug logging failed:", debugError.message)
          }

          resolve(null)
        } else {
          setTimeout(findButton, 1000)
        }
      }

      findButton()
    })
  }

  async function waitForPhotoButton() {
    console.log("Looking for Photos & Videos button...")
    return new Promise((resolve) => {
      let attempts = 0
      const maxAttempts = 15

      const findButton = () => {
        attempts++

        // Look for file input first (most reliable)
        const fileInputs = document.querySelectorAll('input[type="file"]')
        for (const input of fileInputs) {
          if (input.accept && (input.accept.includes("image") || input.accept.includes("video"))) {
            console.log("File input found directly")
            resolve(input)
            return
          }
        }

        // Look for clickable elements that might trigger file input
        const selectors = [
          // Primary selectors
          'li[data-testid="mi-attach-photo"]',
          'div[data-testid="mi-attach-photo"]',
          'button[data-testid="mi-attach-photo"]',

          // Alternative selectors
          'button[aria-label*="Photos"]',
          'button[aria-label*="photos"]',
          'span[title*="Photos"]',
          'div[title*="Photos"]',
          'li[title*="Photos"]',

          // More generic selectors
          '[data-testid="x-viewer"]',
          'li[role="button"]',
          'div[role="button"]',

          // Fallback selectors
          "ul li:first-child",
          'div[role="menu"] > div:first-child',
          'div[role="menu"] li:first-child',
        ]

        for (const selector of selectors) {
          try {
            const elements = document.querySelectorAll(selector)

            for (const element of elements) {
              if (element && element.offsetParent !== null) {
                const elementText =
                  element.textContent || element.getAttribute("aria-label") || element.getAttribute("title") || ""
                const hasPhotoKeyword =
                  elementText.toLowerCase().includes("photo") ||
                  elementText.toLowerCase().includes("image") ||
                  elementText.toLowerCase().includes("video") ||
                  elementText.toLowerCase().includes("media")

                // Check if it's the first item in a menu (photos are usually first)
                const isFirstInMenu =
                  element.matches("li:first-child, div:first-child") || element.previousElementSibling === null

                if (hasPhotoKeyword || isFirstInMenu) {
                  console.log("Photos & Videos button found with selector:", selector)
                  resolve(element)
                  return
                }
              }
            }
          } catch (error) {
            console.log(`Error checking selector ${selector}:`, error.message)
            continue
          }
        }

        if (attempts >= maxAttempts) {
          console.log("Photos & Videos button not found, trying to find any file input...")

          // Last resort: find any file input
          const anyFileInput = document.querySelector('input[type="file"]')
          if (anyFileInput) {
            console.log("Found generic file input as fallback")
            resolve(anyFileInput)
            return
          }

          resolve(null)
        } else {
          setTimeout(findButton, 1000)
        }
      }

      findButton()
    })
  }

  async function uploadMediaFromUrl(mediaUrl) {
    try {
      console.log("Uploading media from URL:", mediaUrl)

      const response = await fetch(mediaUrl, {
        mode: "cors",
        credentials: "omit",
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch media: ${response.status} ${response.statusText}`)
      }

      const blob = await response.blob()

      if (!blob.type.startsWith("image/")) {
        throw new Error(`Invalid media type: ${blob.type}`)
      }

      const fileName = mediaUrl.split("/").pop()?.split("?")[0] || "image.jpg"
      const file = new File([blob], fileName, { type: blob.type })

      const fileInput = document.querySelector(
        'input[type="file"][accept*="image"], input[type="file"][accept*="video"]',
      )

      if (!fileInput) {
        throw new Error("File input not found")
      }

      const dataTransfer = new DataTransfer()
      dataTransfer.items.add(file)
      fileInput.files = dataTransfer.files

      const changeEvent = new Event("change", { bubbles: true })
      fileInput.dispatchEvent(changeEvent)

      console.log("Media file uploaded successfully")
    } catch (error) {
      console.error("Error uploading media:", error)
      throw error
    }
  }

  async function waitForCaptionBox() {
    console.log("Looking for caption input box...")
    return new Promise((resolve) => {
      let attempts = 0
      const maxAttempts = 10

      const findCaptionBox = () => {
        attempts++

        const selectors = [
          '[data-testid="media-caption-input-container"] div[contenteditable="true"]',
          'div[data-testid="caption-input"]',
          'div[contenteditable="true"][data-tab="10"]',
          'div[aria-label*="caption"]',
          'div[placeholder*="caption"]',
          'footer div[contenteditable="true"]',
        ]

        for (const selector of selectors) {
          const element = document.querySelector(selector)
          if (element && element.offsetParent !== null) {
            console.log("Caption box found with selector:", selector)
            resolve(element)
            return
          }
        }

        if (attempts >= maxAttempts) {
          console.log("Caption box not found, proceeding without caption")
          resolve(null)
        } else {
          setTimeout(findCaptionBox, 1000)
        }
      }

      findCaptionBox()
    })
  }

  async function waitForMediaSendButton() {
    console.log("Looking for media send button...")
    return new Promise((resolve) => {
      let attempts = 0
      const maxAttempts = 20 // Increased attempts

      const findSendButton = () => {
        attempts++
        console.log(`Send button search attempt ${attempts}/${maxAttempts}`)

        // First, try the most specific selectors for the media send button
        const specificSelectors = [
          // WhatsApp's specific send button selectors
          '[data-testid="send"]',
          'span[data-testid="send"]',
          'button[data-testid="send"]',
          'div[data-testid="send"]',

          // Send icon selectors
          'span[data-icon="send"]',
          'div[data-icon="send"]',
          'button[data-icon="send"]',

          // Aria label selectors
          'button[aria-label*="Send"]',
          'span[aria-label*="Send"]',
          'div[aria-label*="Send"]',

          // Media-specific send selectors
          'div[data-testid="media-send"]',
          'button[data-testid="media-send"]',
          'span[data-testid="media-send"]',
        ]

        // Try specific selectors first
        for (const selector of specificSelectors) {
          try {
            const elements = document.querySelectorAll(selector)
            console.log(`Checking selector "${selector}": found ${elements.length} elements`)

            for (const element of elements) {
              if (element && element.offsetParent !== null) {
                const rect = element.getBoundingClientRect()
                console.log(`Element found:`, {
                  selector,
                  tagName: element.tagName,
                  className: element.className,
                  visible: rect.width > 0 && rect.height > 0,
                  position: { x: rect.x, y: rect.y, width: rect.width, height: rect.height },
                })

                if (rect.width > 0 && rect.height > 0) {
                  console.log("✅ Media send button found with specific selector:", selector)
                  resolve(element)
                  return
                }
              }
            }
          } catch (error) {
            console.log(`Error with selector ${selector}:`, error.message)
          }
        }

        // If specific selectors fail, try position-based search
        console.log("Specific selectors failed, trying position-based search...")

        try {
          // Look for any clickable element in the bottom-right area
          const allClickable = document.querySelectorAll(`
          button, 
          span[role="button"], 
          div[role="button"],
          span[tabindex="0"],
          div[tabindex="0"],
          [onclick],
          .clickable
        `)

          console.log(`Found ${allClickable.length} clickable elements, checking positions...`)

          for (let i = 0; i < allClickable.length; i++) {
            const element = allClickable[i]
            if (element && element.offsetParent !== null) {
              const rect = element.getBoundingClientRect()

              // Check if element is in bottom-right quadrant and has reasonable size
              const isInBottomRight = rect.right > window.innerWidth * 0.6 && rect.bottom > window.innerHeight * 0.6
              const hasReasonableSize = rect.width >= 20 && rect.height >= 20 && rect.width <= 100 && rect.height <= 100

              if (isInBottomRight && hasReasonableSize) {
                console.log(`Potential send button found at position:`, {
                  index: i,
                  tagName: element.tagName,
                  className: element.className,
                  innerHTML: element.innerHTML.substring(0, 100),
                  position: { x: rect.x, y: rect.y, width: rect.width, height: rect.height },
                  testId: element.getAttribute("data-testid"),
                  ariaLabel: element.getAttribute("aria-label"),
                })

                // Additional checks for send button characteristics
                const innerHTML = element.innerHTML.toLowerCase()
                const hasArrow = innerHTML.includes("svg") || innerHTML.includes("path") || innerHTML.includes("send")
                const hasGreenColor =
                  element.style.backgroundColor?.includes("green") ||
                  element.classList.toString().includes("green") ||
                  getComputedStyle(element).backgroundColor.includes("rgb(37, 211, 102)") // WhatsApp green

                if (hasArrow || hasGreenColor || element.getAttribute("data-testid") === "send") {
                  console.log("✅ Media send button found by position and characteristics")
                  resolve(element)
                  return
                }
              }
            }
          }
        } catch (error) {
          console.log("Position-based search failed:", error.message)
        }

        // Last resort: try to find the send button by looking for SVG elements (send buttons often contain SVG)
        if (attempts > 10) {
          console.log("Last resort: looking for SVG-based send buttons...")
          try {
            const svgElements = document.querySelectorAll("svg")
            for (const svg of svgElements) {
              const parent = svg.closest('button, span[role="button"], div[role="button"], [tabindex="0"]')
              if (parent && parent.offsetParent !== null) {
                const rect = parent.getBoundingClientRect()
                const isInBottomRight = rect.right > window.innerWidth * 0.6 && rect.bottom > window.innerHeight * 0.6

                if (isInBottomRight && rect.width > 0 && rect.height > 0) {
                  console.log("Found SVG-based button in send area:", {
                    tagName: parent.tagName,
                    className: parent.className,
                    position: rect,
                  })
                  resolve(parent)
                  return
                }
              }
            }
          } catch (error) {
            console.log("SVG-based search failed:", error.message)
          }
        }

        // Ultimate fallback: manual click on coordinates
        if (attempts > 15) {
          console.log("Ultimate fallback: trying to click at typical send button coordinates...")
          try {
            // Calculate typical send button position (bottom-right area)
            const x = window.innerWidth - 60 // 60px from right edge
            const y = window.innerHeight - 60 // 60px from bottom edge

            const elementAtPoint = document.elementFromPoint(x, y)
            if (elementAtPoint) {
              console.log("Element found at send button coordinates:", {
                tagName: elementAtPoint.tagName,
                className: elementAtPoint.className,
                innerHTML: elementAtPoint.innerHTML.substring(0, 100),
              })

              // Try to find a clickable parent
              const clickableParent = elementAtPoint.closest(
                'button, span[role="button"], div[role="button"], [tabindex="0"]',
              )
              if (clickableParent) {
                console.log("✅ Found clickable parent at send coordinates")
                resolve(clickableParent)
                return
              } else {
                console.log("✅ Using element at send coordinates directly")
                resolve(elementAtPoint)
                return
              }
            }
          } catch (error) {
            console.log("Coordinate-based search failed:", error.message)
          }
        }

        if (attempts >= maxAttempts) {
          console.log("❌ Media send button not found after all attempts")

          // Final debug: log the entire DOM structure around the media area
          console.log("=== FINAL DEBUG: DOM STRUCTURE ===")
          try {
            const mediaContainer = document.querySelector(
              '[data-testid="media-viewer"], .media-viewer, [data-tab="11"]',
            )
            if (mediaContainer) {
              console.log("Media container HTML:", mediaContainer.outerHTML.substring(0, 1000))
            }

            const footer = document.querySelector("footer")
            if (footer) {
              console.log("Footer HTML:", footer.outerHTML.substring(0, 1000))
            }
          } catch (debugError) {
            console.log("Final debug failed:", debugError.message)
          }

          resolve(null)
        } else {
          setTimeout(findSendButton, 1000)
        }
      }

      findSendButton()
    })
  }

  async function clickAtCoordinates(x, y) {
    console.log(`Attempting to click at coordinates (${x}, ${y})`)

    try {
      // Create and dispatch mouse events
      const mouseDownEvent = new MouseEvent("mousedown", {
        bubbles: true,
        cancelable: true,
        clientX: x,
        clientY: y,
        button: 0,
      })

      const mouseUpEvent = new MouseEvent("mouseup", {
        bubbles: true,
        cancelable: true,
        clientX: x,
        clientY: y,
        button: 0,
      })

      const clickEvent = new MouseEvent("click", {
        bubbles: true,
        cancelable: true,
        clientX: x,
        clientY: y,
        button: 0,
      })

      const element = document.elementFromPoint(x, y)
      if (element) {
        element.dispatchEvent(mouseDownEvent)
        await sleep(50)
        element.dispatchEvent(mouseUpEvent)
        await sleep(50)
        element.dispatchEvent(clickEvent)

        console.log("✅ Click events dispatched at coordinates")
        return true
      }
    } catch (error) {
      console.log("❌ Failed to click at coordinates:", error.message)
    }

    return false
  }

  // Update the waitForMessageBox function to better target the message input
  async function waitForMessageBox() {
    console.log("Looking for message input box...")
    return new Promise((resolve) => {
      let attempts = 0
      const maxAttempts = 15

      const findMessageBox = () => {
        attempts++

        // More specific selectors for the message input box
        const selectors = [
          // Primary selectors - most specific to message input
          '[data-testid="conversation-compose-box-input"]',
          'div[title="Type a message"]',
          'div[data-tab="10"][contenteditable="true"]',

          // Secondary selectors - more specific to message area
          'footer div[contenteditable="true"]',
          'div[data-testid="conversation-panel-body"] + footer div[contenteditable="true"]',
          'div[data-tab="6"] + footer div[contenteditable="true"]',

          // Last resort selectors - check position on page
          'div[contenteditable="true"][spellcheck="true"]',
        ]

        // First try the specific selectors
        for (const selector of selectors) {
          const elements = document.querySelectorAll(selector)
          for (const element of elements) {
            // Check if element is visible and in the bottom part of the screen
            if (
              element &&
              element.offsetParent !== null &&
              element.getBoundingClientRect().width > 0 &&
              element.getBoundingClientRect().top > window.innerHeight / 2
            ) {
              console.log("Message box found with selector:", selector)
              resolve(element)
              return
            }
          }
        }

        // If specific selectors fail, try to find by position and attributes
        const allEditables = document.querySelectorAll('div[contenteditable="true"]')
        for (const element of allEditables) {
          const rect = element.getBoundingClientRect()
          // Check if element is in the bottom third of the screen
          if (element.offsetParent !== null && rect.width > 0 && rect.top > window.innerHeight * 0.6) {
            console.log("Message box found by position")
            resolve(element)
            return
          }
        }

        if (attempts >= maxAttempts) {
          console.log("Message box not found after multiple attempts")
          resolve(null)
        } else {
          setTimeout(findMessageBox, 1000)
        }
      }

      findMessageBox()
    })
  }

  async function waitForSendButton() {
    console.log("Looking for send button...")
    return new Promise((resolve) => {
      let attempts = 0
      const maxAttempts = 10

      const findSendButton = () => {
        attempts++

        const selectors = [
          '[data-testid="send"]',
          'span[data-testid="send"]',
          'button[aria-label*="Send"]',
          'span[data-icon="send"]',
        ]

        for (const selector of selectors) {
          const element = document.querySelector(selector)
          if (element && element.offsetParent !== null && element.getBoundingClientRect().width > 0) {
            console.log("Send button found with selector:", selector)
            resolve(element)
            return
          }
        }

        if (attempts >= maxAttempts) {
          resolve(null)
        } else {
          setTimeout(findSendButton, 1000)
        }
      }

      findSendButton()
    })
  }

  function sleep(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms))
  }

  console.log("Content script setup completed")

  async function waitForWhatsAppToFullyLoad() {
    console.log("Waiting for WhatsApp to fully load...")
    return new Promise((resolve) => {
      let attempts = 0
      const maxAttempts = 30 // Increased wait time

      const checkLoaded = () => {
        attempts++

        // Check multiple indicators that WhatsApp is fully loaded
        const indicators = [
          document.querySelector('[data-testid="side"]'),
          document.querySelector("#side"),
          document.querySelector('[data-testid="chat-list"]'),
          document.querySelector('[data-testid="conversation-compose-box-input"]'),
          document.querySelector('div[title="Type a message"]'),
        ]

        const loadedIndicators = indicators.filter((el) => el !== null)

        if (loadedIndicators.length >= 2) {
          console.log("WhatsApp Web fully loaded")
          resolve()
          return
        }

        if (attempts >= maxAttempts) {
          console.log("WhatsApp load timeout, proceeding anyway...")
          resolve()
        } else {
          setTimeout(checkLoaded, 1000)
        }
      }

      checkLoaded()
    })
  }

  function formatFileSize(bytes) {
    if (bytes === 0) return "0 Bytes"
    const k = 1024
    const sizes = ["Bytes", "KB", "MB", "GB"]
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
  }
})()
