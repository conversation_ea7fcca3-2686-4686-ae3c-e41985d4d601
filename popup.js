document.addEventListener("DOMContentLoaded", () => {
  console.log("=== WhatsApp Automation Extension Loading ===")
  console.log("Extension version: Multi-file support v1.0")
  console.log("Timestamp:", new Date().toISOString())

  // Tab functionality
  const tabs = document.querySelectorAll(".tab")
  const tabContents = document.querySelectorAll(".tab-content")
  const chrome = window.chrome // Declare the chrome variable

  // File upload elements - DISABLED (removed from UI)
  // const fileInput = document.getElementById("fileInput")
  // const fileUploadButton = document.getElementById("fileUploadButton")
  // const fileUploadContainer = document.getElementById("fileUploadContainer")
  // const filePreview = document.getElementById("filePreview")
  // const fileList = document.getElementById("fileList")
  // const clearAllFiles = document.getElementById("clearAllFiles")
  // const fileTypeButtons = document.querySelectorAll(".file-type-btn")

  // File management - store as simple objects instead of File objects
  let selectedFiles = []
  let currentFileType = "all"

  tabs.forEach((tab) => {
    tab.addEventListener("click", () => {
      const targetTab = tab.getAttribute("data-tab")

      // Remove active class from all tabs and contents
      tabs.forEach((t) => t.classList.remove("active"))
      tabContents.forEach((tc) => tc.classList.remove("active"))

      // Add active class to clicked tab and corresponding content
      tab.classList.add("active")
      document.getElementById(`${targetTab}-tab`).classList.add("active")

      // Save active tab to storage
      saveToStorage({ activeTab: targetTab })
    })
  })

  // Manual Tab Elements
  const triggerButton = document.getElementById("trigger")
  const statusDiv = document.getElementById("status")
  const phoneInput = document.getElementById("phoneNumber")
  const messageInput = document.getElementById("message")

  // API Tab Elements
  const fetchQueueButton = document.getElementById("fetchQueue")
  const startBulkButton = document.getElementById("startBulk")
  const stopBulkButton = document.getElementById("stopBulk")
  const apiUrlInput = document.getElementById("apiUrl")
  const userIdInput = document.getElementById("userId")
  const secretInput = document.getElementById("secret")
  const localFolderPathInput = document.getElementById("localFolderPath")
  const testFolderBtn = document.getElementById("testFolderBtn")
  const delaySelect = document.getElementById("delay")
  const queueContainer = document.getElementById("queueContainer")
  const queueList = document.getElementById("queueList")
  const progressContainer = document.getElementById("progress-container")
  const progressBar = document.getElementById("progressBar")
  const progressText = document.getElementById("progressText")

  // Check if all required elements exist
  console.log("=== DOM Elements Check ===")
  const requiredElements = [
    { id: 'apiUrl', element: apiUrlInput },
    { id: 'userId', element: userIdInput },
    { id: 'secret', element: secretInput },
    { id: 'localFolderPath', element: localFolderPathInput },
    { id: 'fetchQueue', element: fetchQueueButton },
    { id: 'startBulk', element: startBulkButton },
    { id: 'stopBulk', element: stopBulkButton },
    { id: 'phoneNumber', element: phoneInput },
    { id: 'message', element: messageInput },
    { id: 'triggerSend', element: triggerButton }
  ]

  const missingElements = requiredElements.filter(item => !item.element)
  if (missingElements.length > 0) {
    console.error("❌ Missing required elements:", missingElements.map(item => item.id))
  } else {
    console.log("✅ All required DOM elements found")
  }

  // Add direct test button and debug area
  const apiTabContent = document.getElementById("api-tab")

  // Create debug area
  const debugArea = document.createElement("div")
  debugArea.style.margin = "10px 0"
  debugArea.style.padding = "10px"
  debugArea.style.backgroundColor = "#f8f9fa"
  debugArea.style.border = "1px solid #ddd"
  debugArea.style.borderRadius = "5px"
  debugArea.id = "debugArea"

  const debugTitle = document.createElement("h4")
  debugTitle.textContent = "Debug Information:"
  debugTitle.style.margin = "0 0 10px 0"

  const debugContent = document.createElement("pre")
  debugContent.style.margin = "0"
  debugContent.style.fontSize = "12px"
  debugContent.style.maxHeight = "200px"
  debugContent.style.overflow = "auto"
  debugContent.style.whiteSpace = "pre-wrap"
  debugContent.style.wordBreak = "break-all"
  debugContent.id = "debugContent"

  debugArea.appendChild(debugTitle)
  debugArea.appendChild(debugContent)

  // Create test buttons container
  const testButtonsContainer = document.createElement("div")
  testButtonsContainer.style.display = "flex"
  testButtonsContainer.style.gap = "10px"
  testButtonsContainer.style.marginTop = "10px"

  const directTestButton = document.createElement("button")
  directTestButton.textContent = "Test API Connection"
  directTestButton.className = "secondary-btn"
  directTestButton.style.flex = "1"
  directTestButton.style.margin = "0"

  const browserTestButton = document.createElement("button")
  browserTestButton.textContent = "Open API in Browser"
  browserTestButton.className = "secondary-btn"
  browserTestButton.style.flex = "1"
  browserTestButton.style.margin = "0"
  browserTestButton.style.backgroundColor = "#17a2b8"

  testButtonsContainer.appendChild(directTestButton)
  testButtonsContainer.appendChild(browserTestButton)

  apiTabContent.insertBefore(testButtonsContainer, fetchQueueButton.nextSibling)
  apiTabContent.insertBefore(debugArea, queueContainer)

  // File Upload Event Listeners - DISABLED (removed from UI)
  // setupFileUpload()

  // API Bulk File Upload Elements - DISABLED (removed from UI)
  // const apiBulkFileInput = document.getElementById("apiBulkFileInput")
  // const apiBulkFileUploadButton = document.getElementById("apiBulkFileUploadButton")
  // const apiBulkFileUploadContainer = document.getElementById("apiBulkFileUploadContainer")
  // const apiBulkFilePreview = document.getElementById("apiBulkFilePreview")
  // const apiBulkFileList = document.getElementById("apiBulkFileList")
  // const apiBulkClearAllFiles = document.getElementById("apiBulkClearAllFiles")
  // const apiBulkFileTypeButtons = document.querySelectorAll("#api-tab .file-type-btn")

  // API Bulk file management
  let apiBulkSelectedFiles = []
  let apiBulkCurrentFileType = "all"

  // Setup API bulk file upload
  function setupApiBulkFileUpload() {
    // DISABLED - UI elements removed
    console.log("API bulk file upload setup skipped - UI removed")
    return
  }

  function updateApiBulkFileInputAccept() {
    const acceptMap = {
      all: "image/*,video/*,audio/*,.pdf,.doc,.docx,.txt,.zip,.rar",
      image: "image/*",
      document: ".pdf,.doc,.docx,.txt,.zip,.rar",
      video: "video/*",
      audio: "audio/*",
    }
    apiBulkFileInput.accept = acceptMap[apiBulkCurrentFileType] || acceptMap.all
  }

  function handleApiBulkFileSelection(files) {
    console.log("=== API BULK FILE SELECTION DEBUG ===")
    console.log("Raw files received:", files)
    console.log("Number of files:", files.length)

    const maxFileSize = 16 * 1024 * 1024 // 16MB
    const validFiles = []

    files.forEach((file, index) => {
      console.log(`Processing API bulk file ${index + 1}:`, file.name, file.size, file.type)

      // Validate file object
      if (!file || !file.name || file.size === undefined || file.size === null) {
        console.error(`Invalid file object at index ${index}:`, file)
        showStatus(`Invalid file object detected at index ${index}`, "error")
        return
      }

      if (file.size > maxFileSize) {
        console.warn(`File too large:`, file.size, "bytes")
        showStatus(`File "${file.name}" is too large. Maximum size is 16MB.`, "error")
        return
      }

      if (file.size === 0) {
        console.warn(`File is empty:`, file.name)
        showStatus(`File "${file.name}" is empty and cannot be uploaded.`, "error")
        return
      }

      if (apiBulkCurrentFileType !== "all" && !isFileTypeAllowed(file, apiBulkCurrentFileType)) {
        console.warn(`File type not allowed:`, file.type, "for filter:", apiBulkCurrentFileType)
        showStatus(`File "${file.name}" is not allowed for selected type.`, "error")
        return
      }

      // Check if file already exists
      const existingFile = apiBulkSelectedFiles.find((f) => f.name === file.name && f.size === file.size)
      if (!existingFile) {
        // Convert File to simple object immediately
        const fileObj = {
          name: file.name,
          type: file.type || "application/octet-stream",
          size: file.size,
          lastModified: file.lastModified || Date.now(),
          file: file, // Keep reference to original File object
        }

        console.log(`API bulk file processed successfully:`, fileObj)
        validFiles.push(fileObj)
      } else {
        console.log(`File already exists, skipping:`, file.name)
      }
    })

    console.log("Valid API bulk files processed:", validFiles.length)

    if (validFiles.length > 0) {
      apiBulkSelectedFiles.push(...validFiles)
      console.log("Total API bulk selected files:", apiBulkSelectedFiles.length)
      updateApiBulkFilePreview()
      saveApiBulkFilesToStorage()
      showStatus(`Added ${validFiles.length} file(s) for bulk sending`, "success")
    } else {
      console.log("No valid files to add for API bulk")
    }
  }

  function updateApiBulkFilePreview() {
    // DISABLED - UI elements removed
    console.log("API bulk file preview update skipped - UI removed")
    return

    if (apiBulkSelectedFiles.length === 0) {
      // apiBulkFilePreview.classList.remove("show")
      return
    }

    apiBulkFilePreview.classList.add("show")
    apiBulkFileList.innerHTML = ""

    apiBulkSelectedFiles.forEach((fileObj, index) => {
      const fileItem = document.createElement("div")
      fileItem.className = "file-item"

      const fileIcon = getFileIcon(fileObj)
      const fileSize = formatFileSize(fileObj.size)

      fileItem.innerHTML = `
      <div class="file-info">
        <div class="file-icon">${fileIcon}</div>
        <div class="file-details">
          <div class="file-name">${fileObj.name}</div>
          <div class="file-size">${fileSize}</div>
        </div>
      </div>
      <button class="file-remove" data-index="${index}">Remove</button>
    `

      apiBulkFileList.appendChild(fileItem)
    })

    // Add remove button listeners
    apiBulkFileList.querySelectorAll(".file-remove").forEach((button) => {
      button.addEventListener("click", (e) => {
        const index = Number.parseInt(e.target.dataset.index)
        apiBulkSelectedFiles.splice(index, 1)
        updateApiBulkFilePreview()
        saveApiBulkFilesToStorage()
      })
    })
  }

  function saveApiBulkFilesToStorage() {
    console.log("Saving API bulk files to storage:", apiBulkSelectedFiles.length)

    if (apiBulkSelectedFiles.length === 0) {
      saveToStorage({ apiBulkSelectedFiles: [] })
      return
    }

    // Convert files to base64 for storage
    const filePromises = apiBulkSelectedFiles.map((fileObj, index) => {
      return new Promise((resolve) => {
        console.log(`Converting API bulk file ${index + 1} for storage:`, fileObj.name)

        // If we don't have the original file object, save what we have
        if (!fileObj.file) {
          console.log(`API bulk file ${index + 1}: No original file object, saving metadata only`)
          const savedData = {
            name: fileObj.name,
            type: fileObj.type,
            size: fileObj.size,
            lastModified: fileObj.lastModified,
            data: fileObj.data || null,
          }
          resolve(savedData)
          return
        }

        // Convert file to base64
        console.log(`API bulk file ${index + 1}: Converting to base64...`)
        const reader = new FileReader()
        reader.onload = (e) => {
          try {
            const result = {
              name: fileObj.name,
              type: fileObj.type,
              size: fileObj.size,
              lastModified: fileObj.lastModified,
              data: e.target.result,
            }
            console.log(`API bulk file ${index + 1} converted successfully`)
            resolve(result)
          } catch (error) {
            console.error(`Error processing API bulk file ${index + 1}:`, error)
            resolve({
              name: fileObj.name,
              type: fileObj.type,
              size: fileObj.size,
              lastModified: fileObj.lastModified,
              data: null,
            })
          }
        }
        reader.onerror = (error) => {
          console.error(`FileReader error for API bulk file ${index + 1}:`, error)
          resolve({
            name: fileObj.name,
            type: fileObj.type,
            size: fileObj.size,
            lastModified: fileObj.lastModified,
            data: null,
          })
        }
        reader.readAsDataURL(fileObj.file)
      })
    })

    Promise.all(filePromises)
      .then((fileDataArray) => {
        console.log("API bulk files processed for storage:", fileDataArray.length)
        const validFileData = fileDataArray.filter((data) => data.data !== null)
        console.log("Valid API bulk files for storage:", validFileData.length)
        saveToStorage({ apiBulkSelectedFiles: validFileData })
      })
      .catch((error) => {
        console.error("Error saving API bulk files to storage:", error)
      })
  }

  async function loadApiBulkFilesFromStorage() {
    console.log("Loading API bulk files from storage...")

    const savedData = await loadFromStorage(["apiBulkSelectedFiles"])
    console.log("Raw saved API bulk data:", savedData)

    if (savedData.apiBulkSelectedFiles && Array.isArray(savedData.apiBulkSelectedFiles)) {
      console.log("Found saved API bulk files:", savedData.apiBulkSelectedFiles.length)

      try {
        apiBulkSelectedFiles = []

        for (let index = 0; index < savedData.apiBulkSelectedFiles.length; index++) {
          const fileData = savedData.apiBulkSelectedFiles[index]
          console.log(`Loading API bulk file ${index + 1}:`, fileData.name)

          try {
            // Validate file data structure
            if (!fileData.name || !fileData.type || fileData.size === undefined) {
              console.warn(`Invalid API bulk file data structure for file ${index + 1}:`, fileData)
              continue
            }

            // Create file object structure
            const fileObj = {
              name: fileData.name,
              type: fileData.type,
              size: fileData.size,
              lastModified: fileData.lastModified || Date.now(),
              data: fileData.data,
              file: null,
            }

            console.log(`API bulk file ${index + 1} loaded successfully:`, fileObj.name)
            apiBulkSelectedFiles.push(fileObj)
          } catch (fileError) {
            console.error(`Error processing API bulk file ${index + 1}:`, fileError)
          }
        }

        console.log("Successfully loaded API bulk files:", apiBulkSelectedFiles.length)
        updateApiBulkFilePreview()
      } catch (error) {
        console.error("Error loading API bulk files from storage:", error)
        apiBulkSelectedFiles = []
      }
    } else {
      console.log("No saved API bulk files found")
    }
  }

  // Global variables for bulk sending
  let messageQueue = []
  let isProcessing = false
  let currentIndex = 0
  let selectedFolderPath = "" // Store folder path

  function setupFileUpload() {
    // File type filter buttons
    fileTypeButtons.forEach((button) => {
      button.addEventListener("click", () => {
        fileTypeButtons.forEach((btn) => btn.classList.remove("active"))
        button.classList.add("active")
        currentFileType = button.dataset.type
        updateFileInputAccept()
      })
    })

    // File upload button click
    fileUploadButton.addEventListener("click", () => {
      fileInput.click()
    })

    // File input change
    fileInput.addEventListener("change", (e) => {
      handleFileSelection(Array.from(e.target.files))
    })

    // Drag and drop
    fileUploadContainer.addEventListener("dragover", (e) => {
      e.preventDefault()
      fileUploadContainer.classList.add("dragover")
    })

    fileUploadContainer.addEventListener("dragleave", (e) => {
      e.preventDefault()
      fileUploadContainer.classList.remove("dragover")
    })

    fileUploadContainer.addEventListener("drop", (e) => {
      e.preventDefault()
      fileUploadContainer.classList.remove("dragover")
      handleFileSelection(Array.from(e.dataTransfer.files))
    })

    // Clear all files
    clearAllFiles.addEventListener("click", () => {
      selectedFiles = []
      updateFilePreview()
      saveFilesToStorage()
    })
  }

  function updateFileInputAccept() {
    const acceptMap = {
      all: "image/*,video/*,audio/*,.pdf,.doc,.docx,.txt,.zip,.rar",
      image: "image/*",
      document: ".pdf,.doc,.docx,.txt,.zip,.rar",
      video: "video/*",
      audio: "audio/*",
    }
    fileInput.accept = acceptMap[currentFileType] || acceptMap.all
  }

  function handleFileSelection(files) {
    console.log("=== FILE SELECTION DEBUG ===")
    console.log("Raw files received:", files)
    console.log("Number of files:", files.length)

    const maxFileSize = 16 * 1024 * 1024 // 16MB
    const validFiles = []

    files.forEach((file, index) => {
      console.log(`\n--- Processing file ${index + 1} ---`)
      console.log("File object:", file)
      console.log("File name:", file?.name)
      console.log("File size:", file?.size)
      console.log("File type:", file?.type)
      console.log("File lastModified:", file?.lastModified)
      console.log("File constructor:", file?.constructor?.name)

      // Validate file object
      if (!file || !file.name || file.size === undefined || file.size === null) {
        console.error(`❌ File ${index + 1} validation failed:`, {
          hasFile: !!file,
          hasName: !!file?.name,
          hasSize: file?.size !== undefined && file?.size !== null,
          file: file,
        })
        showStatus(`Invalid file object detected at index ${index}`, "error")
        return
      }

      if (file.size > maxFileSize) {
        console.warn(`❌ File ${index + 1} too large:`, file.size, "bytes")
        showStatus(`File "${file.name}" is too large. Maximum size is 16MB.`, "error")
        return
      }

      if (file.size === 0) {
        console.warn(`❌ File ${index + 1} is empty`)
        showStatus(`File "${file.name}" is empty and cannot be uploaded.`, "error")
        return
      }

      if (currentFileType !== "all" && !isFileTypeAllowed(file, currentFileType)) {
        console.warn(`❌ File ${index + 1} type not allowed:`, file.type, "for filter:", currentFileType)
        showStatus(`File "${file.name}" is not allowed for selected type.`, "error")
        return
      }

      // Check if file already exists
      const existingFile = selectedFiles.find((f) => f.name === file.name && f.size === file.size)
      if (!existingFile) {
        // Convert File to simple object immediately
        const fileObj = {
          name: file.name,
          type: file.type || "application/octet-stream",
          size: file.size,
          lastModified: file.lastModified || Date.now(),
          file: file, // Keep reference to original File object
        }

        console.log(`✅ File ${index + 1} processed successfully:`, fileObj)
        validFiles.push(fileObj)
      } else {
        console.log(`⚠️ File ${index + 1} already exists, skipping`)
      }
    })

    console.log("\n=== FILE SELECTION SUMMARY ===")
    console.log("Valid files processed:", validFiles.length)
    console.log("Valid files:", validFiles)

    if (validFiles.length > 0) {
      selectedFiles.push(...validFiles)
      console.log("Total selected files after addition:", selectedFiles.length)
      updateFilePreview()
      saveFilesToStorage()
      showStatus(`Added ${validFiles.length} file(s)`, "success")
    } else {
      console.log("No valid files to add")
    }
  }

  function isFileTypeAllowed(file, type) {
    const typeMap = {
      image: file.type.startsWith("image/"),
      video: file.type.startsWith("video/"),
      audio: file.type.startsWith("audio/"),
      document:
        file.type === "application/pdf" ||
        file.type.includes("document") ||
        file.type === "text/plain" ||
        file.type.includes("zip") ||
        file.type.includes("rar"),
    }
    return typeMap[type] || false
  }

  function updateFilePreview() {
    if (selectedFiles.length === 0) {
      filePreview.classList.remove("show")
      return
    }

    filePreview.classList.add("show")
    fileList.innerHTML = ""

    selectedFiles.forEach((fileObj, index) => {
      const fileItem = document.createElement("div")
      fileItem.className = "file-item"

      const fileIcon = getFileIcon(fileObj)
      const fileSize = formatFileSize(fileObj.size)

      fileItem.innerHTML = `
        <div class="file-info">
          <div class="file-icon">${fileIcon}</div>
          <div class="file-details">
            <div class="file-name">${fileObj.name}</div>
            <div class="file-size">${fileSize}</div>
          </div>
        </div>
        <button class="file-remove" data-index="${index}">Remove</button>
      `

      fileList.appendChild(fileItem)
    })

    // Add remove button listeners
    fileList.querySelectorAll(".file-remove").forEach((button) => {
      button.addEventListener("click", (e) => {
        const index = Number.parseInt(e.target.dataset.index)
        selectedFiles.splice(index, 1)
        updateFilePreview()
        saveFilesToStorage()
      })
    })
  }

  function getFileIcon(fileObj) {
    if (fileObj.type.startsWith("image/")) return "🖼️"
    if (fileObj.type.startsWith("video/")) return "🎥"
    if (fileObj.type.startsWith("audio/")) return "🎵"
    if (fileObj.type === "application/pdf") return "📄"
    if (fileObj.type.includes("document")) return "📝"
    if (fileObj.type.includes("zip") || fileObj.type.includes("rar")) return "📦"
    return "📎"
  }

  function formatFileSize(bytes) {
    if (bytes === 0) return "0 Bytes"
    const k = 1024
    const sizes = ["Bytes", "KB", "MB", "GB"]
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
  }

  function saveFilesToStorage() {
    console.log("\n=== SAVE FILES TO STORAGE DEBUG ===")
    console.log("Selected files to save:", selectedFiles.length)

    if (selectedFiles.length === 0) {
      console.log("No files to save, clearing storage")
      saveToStorage({ selectedFiles: [] })
      return
    }

    // Convert files to base64 for storage (only if they have the original file object)
    const filePromises = selectedFiles.map((fileObj, index) => {
      return new Promise((resolve) => {
        console.log(`\n--- Converting file ${index + 1} for storage ---`)
        console.log("File object:", fileObj)
        console.log("Has original file:", !!fileObj.file)
        console.log("Has existing data:", !!fileObj.data)

        // If we don't have the original file object, save what we have
        if (!fileObj.file) {
          console.log(`File ${index + 1}: No original file object, saving metadata only`)
          const savedData = {
            name: fileObj.name,
            type: fileObj.type,
            size: fileObj.size,
            lastModified: fileObj.lastModified,
            data: fileObj.data || null, // May already have base64 data
          }
          console.log(`File ${index + 1} saved data:`, {
            ...savedData,
            data: savedData.data ? `[${savedData.data.length} chars]` : null,
          })
          resolve(savedData)
          return
        }

        // Convert file to base64
        console.log(`File ${index + 1}: Converting to base64...`)
        const reader = new FileReader()
        reader.onload = (e) => {
          try {
            const result = {
              name: fileObj.name,
              type: fileObj.type,
              size: fileObj.size,
              lastModified: fileObj.lastModified,
              data: e.target.result,
            }
            console.log(`✅ File ${index + 1} converted successfully:`, {
              ...result,
              data: result.data ? `[${result.data.length} chars]` : null,
            })
            resolve(result)
          } catch (error) {
            console.error(`❌ Error processing file ${index + 1} for storage:`, fileObj.name, error)
            resolve({
              name: fileObj.name,
              type: fileObj.type,
              size: fileObj.size,
              lastModified: fileObj.lastModified,
              data: null,
            })
          }
        }
        reader.onerror = (error) => {
          console.error(`❌ FileReader error for file ${index + 1}:`, fileObj.name, error)
          resolve({
            name: fileObj.name,
            type: fileObj.type,
            size: fileObj.size,
            lastModified: fileObj.lastModified,
            data: null,
          })
        }
        reader.readAsDataURL(fileObj.file)
      })
    })

    Promise.all(filePromises)
      .then((fileDataArray) => {
        console.log("\n=== STORAGE CONVERSION RESULTS ===")
        console.log("Total files processed:", fileDataArray.length)

        // Filter out files without data
        const validFileData = fileDataArray.filter((data, index) => {
          const isValid = data.data !== null
          console.log(`File ${index + 1} valid for storage:`, isValid)
          if (!isValid) {
            console.warn(`File ${index + 1} has no data, excluding from storage:`, data.name)
          }
          return isValid
        })

        console.log("Valid files for storage:", validFileData.length)
        saveToStorage({ selectedFiles: validFileData })
        console.log(`✅ Saved ${validFileData.length} files to storage`)
      })
      .catch((error) => {
        console.error("❌ Error saving files to storage:", error)
      })
  }

  async function loadFilesFromStorage() {
    console.log("\n=== LOAD FILES FROM STORAGE DEBUG ===")

    const savedData = await loadFromStorage(["selectedFiles"])
    console.log("Raw saved data:", savedData)

    if (savedData.selectedFiles && Array.isArray(savedData.selectedFiles)) {
      console.log("Found saved files:", savedData.selectedFiles.length)

      try {
        selectedFiles = []

        for (let index = 0; index < savedData.selectedFiles.length; index++) {
          const fileData = savedData.selectedFiles[index]
          console.log(`\n--- Loading file ${index + 1} ---`)
          console.log("File data:", { ...fileData, data: fileData.data ? `[${fileData.data.length} chars]` : null })

          try {
            // Validate file data structure
            if (!fileData.name || !fileData.type || fileData.size === undefined) {
              console.warn(`❌ Invalid file data structure for file ${index + 1}:`, fileData)
              continue
            }

            // Create file object structure (without converting back to File object yet)
            const fileObj = {
              name: fileData.name,
              type: fileData.type,
              size: fileData.size,
              lastModified: fileData.lastModified || Date.now(),
              data: fileData.data, // Keep base64 data
              file: null, // No original File object
            }

            console.log(`✅ File ${index + 1} loaded successfully:`, {
              ...fileObj,
              data: fileObj.data ? `[${fileObj.data.length} chars]` : null,
            })
            selectedFiles.push(fileObj)
          } catch (fileError) {
            console.error(`❌ Error processing file ${index + 1}:`, fileData.name, fileError)
          }
        }

        console.log("\n=== LOAD FILES SUMMARY ===")
        console.log("Successfully loaded files:", selectedFiles.length)
        updateFilePreview()
        console.log(`✅ Loaded ${selectedFiles.length} files from storage`)
      } catch (error) {
        console.error("❌ Error loading files from storage:", error)
        selectedFiles = []
      }
    } else {
      console.log("No saved files found or invalid data structure")
    }
  }

  // Convert file objects to File objects when needed for sending
  function convertToFileObjects(fileObjects) {
    console.log("\n=== CONVERT TO FILE OBJECTS DEBUG ===")
    console.log("Input file objects:", fileObjects.length)

    const results = fileObjects
      .map((fileObj, index) => {
        console.log(`\n--- Converting file object ${index + 1} ---`)
        console.log("File object:", { ...fileObj, data: fileObj.data ? `[${fileObj.data.length} chars]` : null })

        try {
          // If we already have a File object, use it
          if (fileObj.file && fileObj.file instanceof File) {
            console.log(`✅ File ${index + 1}: Using existing File object`)
            return fileObj.file
          }

          // If we have base64 data, convert it back to File
          if (fileObj.data) {
            console.log(`File ${index + 1}: Converting from base64 data...`)

            const base64Data = fileObj.data.split(",")[1]
            if (!base64Data) {
              console.warn(`❌ File ${index + 1}: Invalid base64 data format`)
              return null
            }

            console.log(`File ${index + 1}: Base64 data length:`, base64Data.length)

            const byteCharacters = atob(base64Data)
            const byteNumbers = new Array(byteCharacters.length)
            for (let i = 0; i < byteCharacters.length; i++) {
              byteNumbers[i] = byteCharacters.charCodeAt(i)
            }
            const byteArray = new Uint8Array(byteNumbers)

            const file = new File([byteArray], fileObj.name, {
              type: fileObj.type,
              lastModified: fileObj.lastModified,
            })

            console.log(`✅ File ${index + 1}: Successfully converted to File object:`, {
              name: file.name,
              size: file.size,
              type: file.type,
              lastModified: file.lastModified,
            })

            return file
          }

          // If no data available, return null
          console.warn(`❌ File ${index + 1}: No file data available`)
          return null
        } catch (error) {
          console.error(`❌ Error converting file object ${index + 1}:`, fileObj.name, error)
          return null
        }
      })
      .filter((file, index) => {
        const isValid = file !== null
        if (!isValid) {
          console.warn(`File ${index + 1} filtered out (null result)`)
        }
        return isValid
      })

    console.log("\n=== CONVERSION SUMMARY ===")
    console.log("Input objects:", fileObjects.length)
    console.log("Successfully converted:", results.length)
    console.log("Conversion success rate:", `${Math.round((results.length / fileObjects.length) * 100)}%`)

    return results
  }

  // Convert File objects to serializable format for message passing
  async function serializeFilesForMessage(files) {
    console.log("\n=== SERIALIZE FILES FOR MESSAGE PASSING ===")
    console.log("Files to serialize:", files.length)

    const serializedFiles = []

    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      console.log(`\n--- Serializing file ${i + 1} ---`)
      console.log("File:", {
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: file.lastModified,
        isFile: file instanceof File,
      })

      try {
        // Convert File to ArrayBuffer for message passing
        const arrayBuffer = await file.arrayBuffer()
        const uint8Array = new Uint8Array(arrayBuffer)

        const serializedFile = {
          name: file.name,
          type: file.type,
          size: file.size,
          lastModified: file.lastModified,
          data: Array.from(uint8Array), // Convert to regular array for JSON serialization
        }

        console.log(`✅ File ${i + 1} serialized successfully:`, {
          name: serializedFile.name,
          size: serializedFile.size,
          type: serializedFile.type,
          dataLength: serializedFile.data.length,
        })

        serializedFiles.push(serializedFile)
      } catch (error) {
        console.error(`❌ Error serializing file ${i + 1}:`, error)
      }
    }

    console.log("\n=== SERIALIZATION SUMMARY ===")
    console.log("Input files:", files.length)
    console.log("Successfully serialized:", serializedFiles.length)

    return serializedFiles
  }

  // Storage functions
  function saveToStorage(data) {
    try {
      chrome.storage.local.set(data, () => {
        if (chrome.runtime.lastError) {
          console.error("Error saving to storage:", chrome.runtime.lastError)
        }
      })
    } catch (error) {
      console.error("Storage save error:", error)
    }
  }

  function loadFromStorage(keys) {
    return new Promise((resolve) => {
      try {
        chrome.storage.local.get(keys, (result) => {
          if (chrome.runtime.lastError) {
            console.error("Error loading from storage:", chrome.runtime.lastError)
            resolve({})
          } else {
            resolve(result)
          }
        })
      } catch (error) {
        console.error("Storage load error:", error)
        resolve({})
      }
    })
  }

  // Auto-save form data
  function setupAutoSave() {
    // Save API credentials when they change
    apiUrlInput.addEventListener("input", () => {
      saveToStorage({ apiUrl: apiUrlInput.value })
    })

    userIdInput.addEventListener("input", () => {
      saveToStorage({ userId: userIdInput.value })
    })

    secretInput.addEventListener("input", () => {
      saveToStorage({ secret: secretInput.value })
    })

    localFolderPathInput.addEventListener("input", () => {
      saveToStorage({ localFolderPath: localFolderPathInput.value })
    })

    // Test folder path button
    testFolderBtn.addEventListener("click", () => {
      testFolderPath()
    })

    delaySelect.addEventListener("change", () => {
      saveToStorage({ delay: delaySelect.value })
    })

    // Save manual form data
    phoneInput.addEventListener("input", () => {
      saveToStorage({ phoneNumber: phoneInput.value })
    })

    messageInput.addEventListener("input", () => {
      saveToStorage({ message: messageInput.value })
    })
  }

  // Load saved data on startup
  async function loadSavedData() {
    try {
      const savedData = await loadFromStorage([
        "activeTab",
        "apiUrl",
        "userId",
        "secret",
        "localFolderPath",
        "delay",
        "phoneNumber",
        "message",
        "messageQueue",
        "isProcessing",
        "currentIndex",
        "selectedFiles",
        "apiBulkSelectedFiles",
      ])

      console.log("Loading saved data:", savedData)

      // Restore active tab
      if (savedData.activeTab) {
        const targetTab = savedData.activeTab
        tabs.forEach((t) => t.classList.remove("active"))
        tabContents.forEach((tc) => tc.classList.remove("active"))

        const activeTabButton = document.querySelector(`[data-tab="${targetTab}"]`)
        const activeTabContent = document.getElementById(`${targetTab}-tab`)

        if (activeTabButton && activeTabContent) {
          activeTabButton.classList.add("active")
          activeTabContent.classList.add("active")
        }
      }

      // Restore API credentials
      if (savedData.apiUrl) apiUrlInput.value = savedData.apiUrl
      if (savedData.userId) userIdInput.value = savedData.userId
      if (savedData.secret) secretInput.value = savedData.secret
      if (savedData.localFolderPath) localFolderPathInput.value = savedData.localFolderPath
      if (savedData.delay) delaySelect.value = savedData.delay

      // Restore folder path
      if (savedData.localFolderPath) {
        selectedFolderPath = savedData.localFolderPath
        console.log(`Restored folder path: ${selectedFolderPath}`)
      }

      // Restore manual form data
      if (savedData.phoneNumber) phoneInput.value = savedData.phoneNumber
      if (savedData.message) messageInput.value = savedData.message

      // Restore message queue
      if (savedData.messageQueue && Array.isArray(savedData.messageQueue)) {
        messageQueue = savedData.messageQueue
        displayQueue(messageQueue)
        startBulkButton.disabled = messageQueue.length === 0
        showStatus(`Restored ${messageQueue.length} messages from previous session`, "info")
      }

      // Restore processing state
      if (savedData.isProcessing) {
        isProcessing = savedData.isProcessing
        currentIndex = savedData.currentIndex || 0

        if (isProcessing) {
          startBulkButton.style.display = "none"
          stopBulkButton.style.display = "block"
          progressContainer.style.display = "block"
          fetchQueueButton.disabled = true
          showStatus("Resumed bulk sending from previous session", "info")
          updateProgress(currentIndex, messageQueue.length)
        }
      }

      // Load saved files
      await loadFilesFromStorage()

      // Load saved API bulk files
      await loadApiBulkFilesFromStorage()
    } catch (error) {
      console.error("Error loading saved data:", error)
    }
  }

  // Save processing state
  function saveProcessingState() {
    saveToStorage({
      messageQueue: messageQueue,
      isProcessing: isProcessing,
      currentIndex: currentIndex,
    })
  }

  function showStatus(message, type) {
    statusDiv.textContent = message
    statusDiv.className = `status ${type}`
    statusDiv.style.display = "block"

    console.log(`Status: ${type} - ${message}`)

    setTimeout(() => {
      statusDiv.style.display = "none"
    }, 8000) // Increased timeout to see errors longer
  }

  function showDebugInfo(info) {
    const debugContent = document.getElementById("debugContent")
    const debugArea = document.getElementById("debugArea")

    debugContent.textContent = typeof info === "string" ? info : JSON.stringify(info, null, 2)
    debugArea.style.display = "block"
  }

  // Check if content script is ready with better error handling
  async function checkContentScriptReady(tabId, maxRetries = 3) {
    console.log(`Checking content script readiness for tab ${tabId}...`)

    for (let i = 0; i < maxRetries; i++) {
      try {
        const response = await new Promise((resolve, reject) => {
          const timeout = setTimeout(() => {
            reject(new Error("Timeout waiting for content script response"))
          }, 3000)

          chrome.tabs.sendMessage(tabId, { action: "ping" }, (response) => {
            clearTimeout(timeout)

            if (chrome.runtime.lastError) {
              reject(new Error(chrome.runtime.lastError.message))
            } else {
              resolve(response)
            }
          })
        })

        if (response && response.success) {
          console.log("Content script is ready!")
          return true
        }
      } catch (error) {
        console.log(`Content script check attempt ${i + 1} failed:`, error.message)
      }

      // Wait before retrying
      if (i < maxRetries - 1) {
        await new Promise((resolve) => setTimeout(resolve, 2000))
      }
    }

    return false
  }

  // Reload the WhatsApp tab to ensure content script loads
  async function reloadWhatsAppTab(tabId) {
    console.log("Reloading WhatsApp tab to ensure content script loads...")

    try {
      await chrome.tabs.reload(tabId)

      // Wait for reload to complete
      await new Promise((resolve) => setTimeout(resolve, 5000))

      return true
    } catch (error) {
      console.error("Error reloading tab:", error)
      return false
    }
  }

  // Ensure content script is ready with fallback options
  async function ensureContentScript(tabId) {
    try {
      console.log("Ensuring content script is ready...")

      // First attempt: Check if content script is already ready
      let isReady = await checkContentScriptReady(tabId, 2)

      if (!isReady) {
        console.log("Content script not responding, reloading tab...")

        // Reload the tab and try again
        await reloadWhatsAppTab(tabId)

        // Check again after reload
        isReady = await checkContentScriptReady(tabId, 3)

        if (!isReady) {
          throw new Error(
            "Content script failed to load even after tab reload. Please refresh WhatsApp Web manually and try again.",
          )
        }
      }

      return true
    } catch (error) {
      console.error("Error ensuring content script:", error)
      throw error
    }
  }

  function updateProgress(current, total) {
    const percentage = Math.round((current / total) * 100)
    progressBar.style.width = `${percentage}%`
    progressBar.textContent = `${percentage}%`
    progressText.textContent = `Processing ${current} of ${total} messages`

    // Save progress
    saveProcessingState()
  }

  // Validate API credentials
  function validateApiCredentials() {
    const apiUrl = apiUrlInput.value.trim()
    const userId = userIdInput.value.trim()
    const secret = secretInput.value.trim()

    if (!apiUrl || !userId || !secret) {
      showStatus("Please fill in all API credentials", "error")
      return null
    }

    return { apiUrl, userId, secret }
  }

  // Build API URL
  function buildApiUrl(credentials, method = "list_whatsapp_l") {
    let url = credentials.apiUrl
    if (!url.includes("?")) {
      url += "?"
    } else if (!url.endsWith("&") && !url.endsWith("?")) {
      url += "&"
    }

    url += `method=${method}&userid=${encodeURIComponent(credentials.userId)}&secret=${encodeURIComponent(credentials.secret)}`
    return url
  }

  // Direct API fetch function (works around CORS issues)
  async function fetchApiDirect(credentials, method = "list_whatsapp_l", additionalParams = {}) {
    try {
      let url = buildApiUrl(credentials, method)

      // Add additional parameters
      for (const [key, value] of Object.entries(additionalParams)) {
        url += `&${key}=${encodeURIComponent(value)}`
      }

      console.log("Fetching URL:", url)

      // Use different fetch strategies
      const strategies = [
        // Strategy 1: Standard fetch
        () =>
          fetch(url, {
            method: "GET",
            headers: {
              Accept: "*/*",
              "Cache-Control": "no-cache",
            },
            mode: "cors",
          }),

        // Strategy 2: No-cors mode (limited response access)
        () =>
          fetch(url, {
            method: "GET",
            mode: "no-cors",
          }),

        // Strategy 3: Using XMLHttpRequest
        () =>
          new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest()
            xhr.open("GET", url, true)
            xhr.timeout = 30000
            xhr.onload = () => {
              if (xhr.status >= 200 && xhr.status < 300) {
                resolve({
                  ok: true,
                  status: xhr.status,
                  text: () => Promise.resolve(xhr.responseText),
                })
              } else {
                reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText || "Unknown error"}`))
              }
            }
            xhr.onerror = () => reject(new Error("Network error"))
            xhr.ontimeout = () => reject(new Error("Request timeout"))
            xhr.send()
          }),
      ]

      let lastError = null

      for (let i = 0; i < strategies.length; i++) {
        try {
          console.log(`Trying fetch strategy ${i + 1}...`)

          const controller = new AbortController()
          const timeoutId = setTimeout(() => controller.abort(), 30000)

          const response = await strategies[i]()
          clearTimeout(timeoutId)

          console.log("Response status:", response.status)

          if (!response.ok && response.status !== 0) {
            throw new Error(`HTTP ${response.status}: ${response.statusText || "Unknown error"}`)
          }

          const text = await response.text()
          console.log("Response text length:", text.length)
          console.log("Response preview:", text.substring(0, 200))

          return { success: true, text, data: null }
        } catch (error) {
          console.log(`Strategy ${i + 1} failed:`, error.message)
          lastError = error

          if (error.name === "AbortError") {
            throw new Error("Request timed out after 30 seconds")
          }

          // Continue to next strategy
          continue
        }
      }

      throw lastError || new Error("All fetch strategies failed")
    } catch (error) {
      console.error("Direct API fetch error:", error)
      throw error
    }
  }

  // Direct API test function
  async function testApiConnectionDirect() {
    const credentials = validateApiCredentials()
    if (!credentials) return

    directTestButton.disabled = true
    directTestButton.textContent = "Testing..."

    try {
      showStatus("Testing API connection directly...", "info")
      showDebugInfo("Starting direct API test...")

      const result = await fetchApiDirect(credentials)

      if (result.success && result.text) {
        showDebugInfo(
          `Success! Response length: ${result.text.length}\n\nResponse preview:\n${result.text.substring(0, 500)}${result.text.length > 500 ? "...(truncated)" : ""}`,
        )

        try {
          const data = JSON.parse(result.text)
          console.log("Parsed data:", data)

          showStatus("API connection successful! Check debug info below.", "success")

          // Try to parse and display queue
          let queueData = null

          if (Array.isArray(data)) {
            queueData = data
          } else if (data.whatsapp && Array.isArray(data.whatsapp)) {
            queueData = data.whatsapp
          } else if (data.data && Array.isArray(data.data)) {
            queueData = data.data
          } else if (typeof data === "object") {
            // Try to extract array from object
            const possibleArrays = Object.values(data).filter((val) => Array.isArray(val))
            if (possibleArrays.length > 0) {
              queueData = possibleArrays[0]
            }
          }

          if (queueData && queueData.length > 0) {
            // Process the queue data to handle multi fields
            messageQueue = await processQueueWithMultiFiles(queueData)
            displayQueue(messageQueue)
            startBulkButton.disabled = false
            showStatus(`Found ${messageQueue.length} messages in queue!`, "success")
            saveProcessingState() // Save the queue
          } else {
            showStatus("API responded but no queue data found", "error")
            showDebugInfo(`No queue data found in response.\n\nFull response:\n${JSON.stringify(data, null, 2)}`)
          }
        } catch (parseError) {
          console.error("JSON parse error:", parseError)
          showStatus("API responded but returned invalid JSON", "error")
          showDebugInfo(`JSON Parse Error: ${parseError.message}\n\nRaw Response:\n${result.text}`)
        }
      } else {
        throw new Error("No response received")
      }
    } catch (error) {
      console.error("Direct API test error:", error)
      showStatus("API test failed: " + error.message, "error")
      showDebugInfo(
        `Error: ${error.message}\n\nThis could indicate:\n1. Network connectivity issues\n2. CORS restrictions\n3. Invalid API URL or credentials\n4. Server is down`,
      )
    } finally {
      directTestButton.disabled = false
      directTestButton.textContent = "Test API Connection"
    }
  }

  // Open API URL in browser for manual testing
  function openApiInBrowser() {
    const credentials = validateApiCredentials()
    if (!credentials) return

    const url = buildApiUrl(credentials)

    chrome.tabs.create({ url: url }, (tab) => {
      showStatus("Opened API URL in new tab for manual testing", "info")
      showDebugInfo(
        `Opened URL in browser: ${url}\n\nThis will help you see:\n1. If the API is accessible\n2. What the actual response looks like\n3. Any error messages from the server`,
      )
    })
  }

  // Ensure WhatsApp tab is active and ready
  async function ensureWhatsAppTab() {
    console.log("Ensuring WhatsApp tab is ready...")

    const tabs = await chrome.tabs.query({
      url: "https://web.whatsapp.com/*",
    })

    if (tabs.length > 0) {
      // Switch to existing WhatsApp tab
      await chrome.tabs.update(tabs[0].id, { active: true })
      console.log("Switched to existing WhatsApp tab")
      return tabs[0]
    } else {
      // Create new WhatsApp tab
      console.log("Creating new WhatsApp tab...")
      const tab = await chrome.tabs.create({
        url: "https://web.whatsapp.com",
        active: true,
      })

      // Wait for WhatsApp to load
      console.log("Waiting for WhatsApp to load...")
      await new Promise((resolve) => setTimeout(resolve, 8000))

      return tab
    }
  }

  // Improved message sending using content script with longer timeout
  async function sendMessageViaContentScript(tabId, messageData) {
    try {
      const phoneNumber = messageData.mobile || messageData.phone || messageData.number
      console.log(`Sending message to ${phoneNumber}...`, messageData)

      // First navigate to the phone number
      console.log("Navigating to chat...")
      await chrome.tabs.update(tabId, {
        url: `https://web.whatsapp.com/send?phone=${phoneNumber}`,
      })

      // Wait for navigation to complete
      console.log("Waiting for navigation to complete...")
      await new Promise((resolve) => setTimeout(resolve, 8000))

      // Ensure content script is ready
      console.log("Ensuring content script is ready...")
      await ensureContentScript(tabId)

      // Convert file objects to File objects and then serialize for message passing
      if (messageData.files && messageData.files.length > 0) {
        console.log("Converting file objects to File objects...")
        const convertedFiles = convertToFileObjects(messageData.files)
        console.log(`Converted ${convertedFiles.length} files successfully`)

        console.log("Serializing files for message passing...")
        const serializedFiles = await serializeFilesForMessage(convertedFiles)
        messageData.files = serializedFiles
        console.log(`Serialized ${serializedFiles.length} files for content script`)
      }

      // Send message data to content script with increased timeout and progress updates
      console.log("Sending message data to content script...")

      // Increase timeout to 90 seconds for complex messages
      const SEND_TIMEOUT = 120000 // Increased timeout for file uploads

      // Show progress updates to user
      showStatus("Sending message (this may take up to 2 minutes for files)...", "info")

      // Progress indicator
      let progressTimer = null
      let progressSeconds = 0

      progressTimer = setInterval(() => {
        progressSeconds += 5
        showStatus(`Still sending message... (${progressSeconds}s)`, "info")
      }, 5000)

      try {
        const response = await new Promise((resolve, reject) => {
          const timeout = setTimeout(() => {
            reject(new Error("Timeout waiting for message send response"))
          }, SEND_TIMEOUT)

          chrome.tabs.sendMessage(
            tabId,
            {
              action: "sendMessage",
              message: messageData,
              phoneNumber: phoneNumber,
            },
            (response) => {
              clearTimeout(timeout)

              if (chrome.runtime.lastError) {
                console.error("Chrome runtime error:", chrome.runtime.lastError)
                reject(new Error(chrome.runtime.lastError.message))
              } else if (response && response.success) {
                resolve(response)
              } else {
                reject(new Error(response?.error || "Failed to send message"))
              }
            },
          )
        })

        // Clear progress timer
        if (progressTimer) clearInterval(progressTimer)

        console.log("Message sent successfully!")
        return response
      } catch (error) {
        // Clear progress timer on error
        if (progressTimer) clearInterval(progressTimer)
        throw error
      }
    } catch (error) {
      console.error("Error in sendMessageViaContentScript:", error)
      throw error
    }
  }



  // Event listeners for buttons
  function setupEventListeners() {
    // Test buttons
    directTestButton.addEventListener("click", testApiConnectionDirect)
    browserTestButton.addEventListener("click", openApiInBrowser)

    // API Tab - Fetch Queue (using direct fetch instead of background script)
    fetchQueueButton.addEventListener("click", async () => {
    const credentials = validateApiCredentials()
    if (!credentials) return

    fetchQueueButton.disabled = true
    fetchQueueButton.textContent = "Fetching..."

    try {
      showStatus("Fetching message queue directly...", "info")
      console.log("Fetching queue with credentials:", {
        apiUrl: credentials.apiUrl,
        userId: credentials.userId,
        secretLength: credentials.secret ? credentials.secret.length : 0,
      })

      const result = await fetchApiDirect(credentials)

      if (result.success && result.text) {
        try {
          const data = JSON.parse(result.text)
          console.log("Parsed queue data:", data)

          // Try multiple paths to find the queue data
          let queueData = null

          if (Array.isArray(data)) {
            queueData = data
          } else if (data.whatsapp && Array.isArray(data.whatsapp)) {
            queueData = data.whatsapp
          } else if (data.data && Array.isArray(data.data)) {
            queueData = data.data
          } else if (typeof data === "object") {
            // Try to extract array from object
            const possibleArrays = Object.values(data).filter((val) => Array.isArray(val))
            if (possibleArrays.length > 0) {
              queueData = possibleArrays[0]
            }
          }

          if (!queueData) {
            console.error("Could not find queue data in response:", data)
            throw new Error("Invalid queue data format from API")
          }

          // Process the queue data to handle multi fields
          messageQueue = await processQueueWithMultiFiles(queueData)

          displayQueue(messageQueue)
          startBulkButton.disabled = messageQueue.length === 0
          showStatus(`Found ${messageQueue.length} messages in queue`, "success")

          // Save the fetched queue
          saveProcessingState()
        } catch (parseError) {
          console.error("JSON parse error:", parseError)
          showStatus("API responded but returned invalid JSON", "error")
          showDebugInfo(`JSON Parse Error: ${parseError.message}\n\nRaw Response:\n${result.text}`)
        }
      } else {
        throw new Error("No response received from API")
      }
    } catch (error) {
      console.error("Error fetching queue:", error)
      showStatus("Error fetching message queue: " + error.message, "error")
      showDebugInfo(
        `Direct Fetch Error: ${error.message}\n\nTry using the "Test API Connection" button to debug further.`,
      )
    } finally {
      fetchQueueButton.disabled = false
      fetchQueueButton.textContent = "Fetch Message Queue"
    }
  })

  // API Tab - Start Bulk Sending
  startBulkButton.addEventListener("click", async () => {
    if (messageQueue.length === 0) {
      showStatus("No messages in queue", "error")
      return
    }

    try {
      await ensureWhatsAppTab()
    } catch (error) {
      showStatus("Error preparing WhatsApp tab", "error")
      return
    }

    isProcessing = true
    currentIndex = 0
    startBulkButton.style.display = "none"
    stopBulkButton.style.display = "block"
    progressContainer.style.display = "block"
    fetchQueueButton.disabled = true

    // Save processing state
    saveProcessingState()

    processBulkMessages()
  })

  // API Tab - Stop Bulk Sending
  stopBulkButton.addEventListener("click", () => {
    isProcessing = false
    startBulkButton.style.display = "block"
    stopBulkButton.style.display = "none"
    progressContainer.style.display = "none"
    fetchQueueButton.disabled = false
    showStatus("Bulk sending stopped", "info")

    // Save stopped state
    saveProcessingState()
  })

  // Manual Tab - Send Message
  triggerButton.addEventListener("click", async () => {
    const phoneNumber = phoneInput.value.trim()
    const message = messageInput.value.trim()

    if (!phoneNumber) {
      showStatus("Please enter a phone number", "error")
      return
    }

    if (!message && selectedFiles.length === 0) {
      showStatus("Please enter a message or select files to send", "error")
      return
    }

    triggerButton.disabled = true
    triggerButton.textContent = "Sending..."

    try {
      showStatus("Preparing to send message...", "info")

      // Ensure WhatsApp tab is ready
      const tab = await ensureWhatsAppTab()

      // Prepare message data
      const messageData = {
        message: message,
        files: selectedFiles.length > 0 ? selectedFiles : null,
      }

      // Send message
      const result = await sendMessageViaContentScript(tab.id, messageData)

      if (result.success) {
        showStatus("Message sent successfully!", "success")
        // Clear form after successful send
        messageInput.value = ""
        selectedFiles = []
        updateFilePreview()
        saveFilesToStorage()
        saveToStorage({ message: "" })
      } else {
        throw new Error(result.error || "Unknown error occurred")
      }
    } catch (error) {
      console.error("Error sending message:", error)
      showStatus("Error sending message: " + error.message, "error")
    } finally {
      triggerButton.disabled = false
      triggerButton.textContent = "Send Message"
    }
  })
  }

  async function updateMessageStatus(messageId, status) {
    const credentials = validateApiCredentials()
    if (!credentials) return { success: false }

    try {
      const result = await fetchApiDirect(credentials, "update_status", {
        id: messageId,
        status: status,
      })

      if (result.success) {
        try {
          const data = JSON.parse(result.text)
          return { success: true, data }
        } catch (e) {
          return { success: false, error: "Invalid JSON response" }
        }
      } else {
        return { success: false, error: "Request failed" }
      }
    } catch (error) {
      console.error("Error updating message status:", error)
      return { success: false, error: error.message }
    }
  }

  // Update the displayQueue function to show message, caption, and link
  function displayQueue(queue) {
    queueContainer.style.display = "block"
    queueList.innerHTML = ""

    if (queue.length === 0) {
      queueList.innerHTML = "<p>No messages in queue</p>"
      return
    }

    // Add summary at the top
    const individualCount = queue.filter(item => item.multiType === 'individual').length
    const regularCount = queue.filter(item => !item.multiType).length
    const totalFileRefs = queue.reduce((sum, item) => sum + (item.fileRefs ? item.fileRefs.length : (item.files ? item.files.length : 0)), 0)

    const summary = document.createElement("div")
    summary.style.cssText = `
      background: #e8f5e8;
      padding: 10px;
      margin-bottom: 15px;
      border-radius: 5px;
      border: 1px solid #25d366;
      font-size: 14px;
    `

    summary.innerHTML = `
      <strong>📊 Queue Summary:</strong> ${queue.length} total messages | ${totalFileRefs} total files<br>
      ${regularCount > 0 ? `📝 Regular: ${regularCount} | ` : ''}${individualCount > 0 ? `📦 Multi-field messages: ${individualCount}` : ''}
    `

    queueList.appendChild(summary)

    queue.forEach((item, index) => {
      const queueItem = document.createElement("div")
      queueItem.className = "queue-item"
      queueItem.style.marginBottom = "15px"
      queueItem.style.padding = "10px"
      queueItem.style.border = "1px solid #ddd"
      queueItem.style.borderRadius = "5px"
      queueItem.style.backgroundColor = "#f9f9f9"

      // Debug the item structure
      console.log(`Queue item ${index}:`, item)

      // Fix the string handling in displayQueue function
      const phoneNumber = item.mobile || item.phone || item.number || item.to || "N/A"
      const message = (item.message || "No message").toString()
      const caption = (item.caption || "No caption").toString()
      const link = (item.link || "No media").toString()
      const status = (item.status || "Pending").toString()

      // Determine file type and icon
      let typeIcon = "📝"
      let typeInfo = ""

      if (item.multiType) {
        switch (item.multiType) {
          case 'individual':
            if (item.fileRefs && item.fileRefs[0]) {
              typeIcon = item.fileRefs[0].isCloud ? "☁️" : "📁"
              typeInfo = ` (${item.fileRefs[0].isCloud ? 'External URL' : 'Local file'} - ${item.multiIndex}/${item.totalMultiItems})`
            } else {
              typeIcon = "📦"
              typeInfo = ` (Multi-item ${item.multiIndex}/${item.totalMultiItems})`
            }
            break
          case 'combined':
            typeIcon = "📦"
            typeInfo = ` (${item.fileCount || 0} files combined)`
            break
          case 'cloud':
            typeIcon = "☁️"
            typeInfo = " (Cloud file)"
            break
          case 'local':
            typeIcon = "📁"
            typeInfo = " (Local file)"
            break
          case 'local_text':
            typeIcon = "📄"
            typeInfo = " (Text-only, no file)"
            break
        }
      }

      // File information - show file references
      let fileInfo = ""
      if (item.fileRefs && item.fileRefs.length > 0) {
        const fileNames = item.fileRefs.map((ref) => {
          const icon = ref.isCloud ? "☁️" : "📁"
          return `${icon} ${ref.link}`
        }).join(", ")
        fileInfo = `<div style="margin-bottom: 5px;"><strong>📎 Files (${item.fileRefs.length}):</strong> ${fileNames}${typeInfo}</div>`
      } else if (item.files && item.files.length > 0) {
        // Fallback for items with actual files
        if (item.files.length === 1) {
          fileInfo = `<div style="margin-bottom: 5px;"><strong>📎 File:</strong> ${item.files[0].name || "Unknown file"}${typeInfo}</div>`
        } else {
          const fileNames = item.files.map((f) => f.name || "Unknown file").join(", ")
          fileInfo = `<div style="margin-bottom: 5px;"><strong>📎 Files (${item.files.length}):</strong> ${fileNames}${typeInfo}</div>`
        }
      }

      queueItem.innerHTML = `
  <div style="margin-bottom: 5px;"><strong>${typeIcon} Phone:</strong> ${phoneNumber}${item.isMultiItem ? ` (Multi-item from ${item.originalId})` : ''}</div>
  <div style="margin-bottom: 5px;"><strong>💬 Message:</strong> ${message.length > 100 ? message.substring(0, 100) + "..." : message}</div>
  <div style="margin-bottom: 5px;"><strong>📝 Caption:</strong> ${caption.length > 50 ? caption.substring(0, 50) + "..." : caption}</div>
  ${fileInfo}
  <div style="margin-bottom: 5px;"><strong>🔗 Media:</strong> ${link !== "No media" && link.trim() ? `<a href="${link}" target="_blank" style="color: #007bff; text-decoration: none;">View Media</a>${typeInfo}` : "No media"}</div>
  <div><strong>📊 Status:</strong> <span style="color: ${status === "0" ? "#ffc107" : status === "sent" ? "#28a745" : "#dc3545"};">${status === "0" ? "Pending" : status}</span></div>
`
      queueList.appendChild(queueItem)
    })
  }

  // Update the processBulkMessages function to handle the new message format
  async function processBulkMessages() {
    if (!isProcessing || currentIndex >= messageQueue.length) {
      isProcessing = false
      startBulkButton.style.display = "block"
      stopBulkButton.style.display = "none"
      progressContainer.style.display = "none"
      fetchQueueButton.disabled = false
      showStatus("Bulk sending completed!", "success")

      // Clear processing state
      saveToStorage({
        isProcessing: false,
        currentIndex: 0,
      })
      return
    }

    const currentMessage = messageQueue[currentIndex]
    updateProgress(currentIndex + 1, messageQueue.length)

    try {
      const tabs = await chrome.tabs.query({
        url: "https://web.whatsapp.com/*",
      })

      if (tabs.length === 0) {
        throw new Error("WhatsApp tab not found")
      }

      const tab = tabs[0]

      // Validate message data
      const phoneNumber = currentMessage.mobile || currentMessage.phone || currentMessage.number || currentMessage.to
      if (!phoneNumber) {
        throw new Error("No phone number found in message data")
      }

      // Check if we have any content to send
      const hasMessage = currentMessage.message && currentMessage.message.toString().trim()
      const hasCaption = currentMessage.caption && currentMessage.caption.toString().trim()
      const hasLink = currentMessage.link && currentMessage.link.toString().trim()

      if (!hasMessage && !hasCaption && !hasLink && apiBulkSelectedFiles.length === 0) {
        throw new Error("No content to send (no message, caption, link, or files)")
      }

      console.log(`Processing bulk message ${currentIndex + 1}:`, {
        phone: phoneNumber,
        hasMessage,
        hasCaption,
        hasLink,
        filesCount: apiBulkSelectedFiles.length,
      })

      // Load files from references if needed
      let filesToSend = []

      if (currentMessage.fileRefs && currentMessage.fileRefs.length > 0) {
        console.log(`Loading ${currentMessage.fileRefs.length} files from references...`)
        filesToSend = await loadFilesFromReferences(currentMessage.fileRefs)
      } else if (apiBulkSelectedFiles.length > 0) {
        filesToSend = apiBulkSelectedFiles
      }

      // Add files to the message data
      const messageDataWithFiles = {
        ...currentMessage,
        files: filesToSend.length > 0 ? filesToSend : null,
      }

      console.log("Sending message with files:", {
        phone: phoneNumber,
        filesCount: filesToSend.length,
        hasFileRefs: !!(currentMessage.fileRefs && currentMessage.fileRefs.length > 0)
      })

      // Send the complete message data including files
      await sendMessageViaContentScript(tab.id, messageDataWithFiles)

      // Update message status via API
      if (currentMessage.id) {
        const statusResult = await updateMessageStatus(currentMessage.id, "sent")
        console.log("Status update result:", statusResult)
      }

      console.log(`Bulk message sent successfully to ${phoneNumber}`)
      showStatus(
        `Sent ${currentIndex + 1}/${messageQueue.length} messages (with ${filesToSend.length} files)`,
        "success",
      )
    } catch (error) {
      console.error(`Error sending bulk message ${currentIndex + 1}:`, error)

      // Update message status to failed
      if (currentMessage.id) {
        await updateMessageStatus(currentMessage.id, "failed")
      }

      showStatus(`Error on message ${currentIndex + 1}: ${error.message}`, "error")
    }

    currentIndex++

    // Save current progress
    saveProcessingState()

    // Wait for the specified delay before processing next message
    const delay = Number.parseInt(delaySelect.value) * 1000
    setTimeout(() => {
      if (isProcessing) {
        processBulkMessages()
      }
    }, delay)
  }

  // Process queue data with multi files support - SEND ALL FILES TO EACH MOBILE
  async function processQueueWithMultiFiles(queueData) {
    console.log("🔄 Processing queue with multi files support...")
    console.log("📋 Original queue data:", queueData)
    const processedQueue = []

    for (const item of queueData) {
      if (item.multi && item.multi.trim()) {
        try {
          console.log("🔍 Processing multi field for item:", item.id, "mobile:", item.mobile)
          console.log("📄 Multi field content:", item.multi)

          // Parse the multi field
          let parsedMulti
          try {
            parsedMulti = typeof item.multi === 'string' ? JSON.parse(item.multi) : item.multi
          } catch (parseError) {
            throw new Error("Invalid multi data format: " + parseError.message)
          }

          if (!parsedMulti.whatsapp || !Array.isArray(parsedMulti.whatsapp)) {
            throw new Error("Invalid multi data structure: missing whatsapp array")
          }

          // Collect ALL file references for this mobile number (don't load files yet)
          // Create SEPARATE messages for each file in multi field (both external URLs and local files)
          let messageIndex = 0

          for (const multiItem of parsedMulti.whatsapp) {
            if (!multiItem.link) continue

            messageIndex++

            // Process BOTH external URLs and local files
            const isCloudFile = multiItem.link.startsWith('http://') || multiItem.link.startsWith('https://')

            // Create separate queue item for each file (external or local)
            const queueItem = {
              ...item,
              id: `${item.id}_multi_${messageIndex}`,
              message: multiItem.message || item.message || '',
              caption: multiItem.caption || item.caption || '',
              fileRefs: [{
                link: multiItem.link,
                caption: multiItem.caption || item.caption || '',
                message: multiItem.message || item.message || '',
                isCloud: isCloudFile,
                isLocal: !isCloudFile
              }],
              files: [], // Will be populated when sending
              multiType: 'individual',
              originalId: item.id,
              isMultiItem: true,
              fileCount: 1,
              multiIndex: messageIndex,
              totalMultiItems: parsedMulti.whatsapp.filter(mi => mi.link).length
            }

            processedQueue.push(queueItem)
            const fileType = isCloudFile ? 'external URL' : 'local file'
            console.log(`✅ Created individual item ${messageIndex} for ${item.mobile}: ${multiItem.link} (${fileType}) with caption: "${multiItem.caption}"`)
            console.log(`📦 Queue item details:`, queueItem)
          }

        } catch (error) {
          console.error("Error processing multi field:", error)
          // Add original item if multi processing fails
          processedQueue.push(item)
        }
      } else {
        // No multi field, add original item
        processedQueue.push(item)
      }
    }

    console.log("🎉 Queue processing complete:", {
      original: queueData.length,
      processed: processedQueue.length,
      totalFileRefs: processedQueue.reduce((sum, item) => sum + (item.fileRefs ? item.fileRefs.length : 0), 0)
    })

    console.log("📋 Final processed queue:", processedQueue)

    // Update the display immediately after processing
    displayQueue(processedQueue)

    return processedQueue
  }

  // Helper functions for multi field processing
  function isCloudFile(link) {
    return link && (link.startsWith('http://') || link.startsWith('https://'))
  }

  async function downloadCloudFileInternal(url) {
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage({
        action: "downloadCloudFile",
        url: url
      }, (response) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message))
        } else if (!response || !response.success) {
          reject(new Error(response?.error || "Download failed"))
        } else {
          // Return in the expected format
          resolve({
            success: true,
            file: response.fileData
          })
        }
      })
    })
  }





  // Convert File object to our data format
  async function convertFileToData(file) {
    return new Promise((resolve) => {
      const reader = new FileReader()
      reader.onload = (e) => {
        resolve({
          name: file.name,
          type: file.type,
          size: file.size,
          lastModified: file.lastModified,
          data: e.target.result
        })
      }
      reader.onerror = () => {
        resolve({
          name: file.name,
          type: file.type,
          size: file.size,
          lastModified: file.lastModified,
          data: null
        })
      }
      reader.readAsDataURL(file)
    })
  }

  // Test folder path (simple text input approach)
  function testFolderPath() {
    const folderPath = localFolderPathInput.value.trim()

    if (!folderPath) {
      showStatus("Please enter a folder path", "error")
      return
    }

    console.log("Testing folder path:", folderPath)

    // Store the folder path
    selectedFolderPath = folderPath

    // Save to storage
    saveToStorage({
      localFolderPath: folderPath
    })

    showStatus(`✅ Folder path set: "${folderPath}" - Files will be loaded when sending messages`, "success")

    console.log("✅ Folder path stored:", folderPath)
    console.log("📋 Files will be loaded dynamically when needed (no screen freeze)")

    // Show instructions
    showStatus(
      `Folder path set to: ${folderPath}\n\n` +
      `Make sure this folder contains files referenced in your API:\n` +
      `• 1.jpeg\n• 2.jpeg\n• (other files from multi field)\n\n` +
      `Files will be loaded automatically when sending messages.`,
      "success"
    )
  }



  // Store folder files reference for later use
  let folderFilesCache = new Map()

  // Get file from folder when needed (ask user to select specific file)
  async function getFileFromFolder(filename) {
    // Check if we have the file in cache
    if (folderFilesCache.has(filename)) {
      console.log(`✅ Using cached file: ${filename}`)
      return folderFilesCache.get(filename)
    }

    console.log(`Loading file: ${filename} from folder: ${selectedFolderPath}`)

    return new Promise((resolve, reject) => {
      // Create file input for specific file
      const fileInput = document.createElement('input')
      fileInput.type = 'file'
      fileInput.accept = 'image/*,video/*,audio/*,.pdf,.doc,.docx,.txt,.zip,.rar'

      // Show user which file to select
      const message = `Please select the file: ${filename}\n\nFrom folder: ${selectedFolderPath}\n\nThis file is needed for the current message.`

      if (!confirm(message)) {
        reject(new Error(`User cancelled selection for ${filename}`))
        return
      }

      fileInput.onchange = (e) => {
        const selectedFile = e.target.files[0]
        if (selectedFile) {
          // Cache the file for future use
          folderFilesCache.set(filename, selectedFile)
          console.log(`✅ File selected and cached: ${filename} -> ${selectedFile.name}`)
          resolve(selectedFile)
        } else {
          reject(new Error(`No file selected for ${filename}`))
        }
      }

      fileInput.oncancel = () => {
        reject(new Error(`File selection cancelled for ${filename}`))
      }

      // Trigger file selection
      fileInput.click()
    })
  }

  // Load actual files from file references when sending
  async function loadFilesFromReferences(fileRefs) {
    console.log(`Loading ${fileRefs.length} files from references...`)
    const loadedFiles = []

    for (const fileRef of fileRefs) {
      try {
        if (fileRef.isCloud) {
          // Download external URL file
          console.log("Downloading external file:", fileRef.link)
          const downloadResult = await downloadCloudFileInternal(fileRef.link)

          if (downloadResult && downloadResult.success) {
            // Add caption and message to the downloaded file
            downloadResult.file.caption = fileRef.caption || ''
            downloadResult.file.message = fileRef.message || ''

            loadedFiles.push(downloadResult.file)
            console.log(`✅ External file downloaded: ${fileRef.link} with caption: "${fileRef.caption}"`)
          } else {
            console.warn(`⚠️ Failed to download external file: ${fileRef.link}`)
          }
        } else if (fileRef.isLocal) {
          // Load local file from user's folder
          console.log("Loading local file:", fileRef.link)
          const localFile = await getFileFromFolder(fileRef.link)
          const fileData = await convertFileToData(localFile)

          // Add caption and message to the file data
          fileData.caption = fileRef.caption || ''
          fileData.message = fileRef.message || ''

          loadedFiles.push(fileData)
          console.log(`✅ Local file loaded: ${fileRef.link} with caption: "${fileRef.caption}"`)
        }
      } catch (error) {
        console.error(`❌ Error loading file ${fileRef.link}:`, error)
        // Continue with other files even if one fails
      }
    }

    console.log(`✅ Loaded ${loadedFiles.length}/${fileRefs.length} files successfully (external + local)`)
    return loadedFiles
  }

  // Initialize the extension
  async function initialize() {
    console.log("Initializing WhatsApp Automation Extension...")

    // Load saved data first
    await loadSavedData()

    // Setup auto-save for form inputs
    setupAutoSave()

    // Add API bulk file setup to initialization - DISABLED (removed from UI)
    // setupApiBulkFileUpload()

    // Setup all event listeners
    setupEventListeners()

    console.log("Extension initialized successfully!")
  }

  // Start initialization
  initialize()
})
