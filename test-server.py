#!/usr/bin/env python3
"""
Simple test server for WhatsApp Automation Extension
Serves the test JSON file as an API endpoint
"""

import http.server
import socketserver
import json
import urllib.parse
import os
from datetime import datetime

PORT = 8000

class TestAPIHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        # Parse the URL and query parameters
        parsed_url = urllib.parse.urlparse(self.path)
        query_params = urllib.parse.parse_qs(parsed_url.query)
        
        print(f"\n[{datetime.now().strftime('%H:%M:%S')}] Request: {self.path}")
        print(f"Query params: {query_params}")
        
        # Check if this is an API request
        if parsed_url.path == '/api' or parsed_url.path == '/api/':
            self.handle_api_request(query_params)
        else:
            # Serve static files
            super().do_GET()
    
    def handle_api_request(self, query_params):
        """Handle API requests for WhatsApp automation"""
        
        # Check for required parameters
        method = query_params.get('method', [''])[0]
        userid = query_params.get('userid', [''])[0]
        secret = query_params.get('secret', [''])[0]
        
        print(f"API Request - Method: {method}, UserID: {userid}, Secret: {secret}")
        
        # Set CORS headers
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        
        # Validate parameters
        if not method or not userid or not secret:
            error_response = {
                "success": False,
                "error": "Missing required parameters: method, userid, secret"
            }
            self.wfile.write(json.dumps(error_response, indent=2).encode())
            return
        
        # Simple validation
        if method != 'list_whatsapp_l':
            error_response = {
                "success": False,
                "error": f"Unknown method: {method}"
            }
            self.wfile.write(json.dumps(error_response, indent=2).encode())
            return
        
        if userid != 'test-user' or secret != 'test-secret':
            error_response = {
                "success": False,
                "error": "Invalid credentials"
            }
            self.wfile.write(json.dumps(error_response, indent=2).encode())
            return
        
        # Load and return test data
        try:
            with open('test-local-files.json', 'r') as f:
                test_data = json.load(f)
            
            print(f"Returning {len(test_data['whatsapp'])} test messages")
            self.wfile.write(json.dumps(test_data, indent=2).encode())
            
        except FileNotFoundError:
            error_response = {
                "success": False,
                "error": "Test data file not found"
            }
            self.wfile.write(json.dumps(error_response, indent=2).encode())
        except Exception as e:
            error_response = {
                "success": False,
                "error": f"Server error: {str(e)}"
            }
            self.wfile.write(json.dumps(error_response, indent=2).encode())
    
    def do_OPTIONS(self):
        """Handle CORS preflight requests"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def log_message(self, format, *args):
        """Override to customize logging"""
        return  # Suppress default logging

if __name__ == "__main__":
    print("=" * 60)
    print("🚀 WhatsApp Automation Test Server")
    print("=" * 60)
    print(f"📡 Server starting on port {PORT}")
    print(f"🌐 API endpoint: http://localhost:{PORT}/api")
    print(f"📋 Test credentials:")
    print(f"   User ID: test-user")
    print(f"   Secret: test-secret")
    print(f"   Method: list_whatsapp_l")
    print()
    print(f"🔗 Full test URL:")
    print(f"   http://localhost:{PORT}/api?method=list_whatsapp_l&userid=test-user&secret=test-secret")
    print()
    print(f"📁 Make sure 'test-local-files.json' is in the same directory")
    print(f"🛑 Press Ctrl+C to stop the server")
    print("=" * 60)
    
    # Check if test file exists
    if not os.path.exists('test-local-files.json'):
        print("❌ ERROR: test-local-files.json not found!")
        print("   Please make sure the test file is in the same directory")
        exit(1)
    
    try:
        with socketserver.TCPServer(("", PORT), TestAPIHandler) as httpd:
            print(f"✅ Server running at http://localhost:{PORT}")
            print("   Waiting for requests...")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Server error: {e}")
