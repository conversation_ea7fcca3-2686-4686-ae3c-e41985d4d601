Stack trace:
Frame         Function      Args
0007FFFF35C0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF24C0) msys-2.0.dll+0x2118E
0007FFFF35C0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFF35C0  0002100469F2 (00021028DF99, 0007FFFF3478, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF35C0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF35C0  00021006A545 (0007FFFF35D0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF35D0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFC9AD60000 ntdll.dll
7FFC9A380000 KERNEL32.DLL
7FFC982C0000 KERNELBASE.dll
7FFC99050000 USER32.dll
7FFC98800000 win32u.dll
7FFC9ACF0000 GDI32.dll
7FFC97EB0000 gdi32full.dll
7FFC98210000 msvcp_win.dll
7FFC98830000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFC99220000 advapi32.dll
7FFC996F0000 msvcrt.dll
7FFC98C30000 sechost.dll
7FFC9A760000 RPCRT4.dll
7FFC97490000 CRYPTBASE.DLL
7FFC98170000 bcryptPrimitives.dll
7FFC9AC50000 IMM32.DLL
