// Enhanced background.js with file handling support

console.log("WhatsApp Automation Background Script v3.0 loaded")

const chrome = window.chrome

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log("Background received:", request.action, request)

  switch (request.action) {
    case "fetchQueue":
      handleFetchQueue(request, sendResponse)
      break
    case "directFetch":
      handleDirectFetch(request, sendResponse)
      break
    case "updateStatus":
      handleUpdateStatus(request, sendResponse)
      break
    case "downloadCloudFile":
      handleDownloadCloudFile(request, sendResponse)
      break
    case "processLocalFile":
      handleProcessLocalFile(request, sendResponse)
      break
    case "processMultiFiles":
      handleProcessMultiFiles(request, sendResponse)
      break
    case "contentScriptReady":
      console.log("Content script is ready")
      sendResponse({ success: true })
      break
    default:
      sendResponse({ success: false, error: "Unknown action" })
  }

  return true
})

async function handleDirectFetch(request, sendResponse) {
  try {
    const { url } = request

    if (!url) {
      throw new Error("Missing URL parameter")
    }

    console.log("Direct fetch URL:", url)

    const response = await fetch(url, {
      method: "GET",
      headers: {
        Accept: "*/*",
        "Cache-Control": "no-cache",
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const text = await response.text()
    console.log("Direct fetch response:", text.substring(0, 200) + (text.length > 200 ? "..." : ""))

    try {
      const data = JSON.parse(text)
      sendResponse({ success: true, data: data, text: text })
    } catch (e) {
      sendResponse({ success: false, error: "Invalid JSON", text: text })
    }
  } catch (error) {
    console.error("Direct fetch error:", error)
    sendResponse({ success: false, error: error.message })
  }
}

async function handleFetchQueue(request, sendResponse) {
  try {
    const { apiUrl, userId, secret } = request

    if (!apiUrl || !userId || !secret) {
      throw new Error("Missing required parameters")
    }

    console.log("Fetching queue with params:", { apiUrl, userId })

    let url = apiUrl
    if (!url.includes("?")) {
      url += "?"
    } else if (!url.endsWith("&") && !url.endsWith("?")) {
      url += "&"
    }

    url += `method=list_whatsapp_l&userid=${encodeURIComponent(userId)}&secret=${encodeURIComponent(secret)}`
    console.log("Full API URL:", url)

    console.log("Starting fetch request...")
    const response = await fetch(url, {
      method: "GET",
      headers: {
        Accept: "*/*",
        "Cache-Control": "no-cache",
      },
    })

    console.log("Fetch response status:", response.status, response.statusText)

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const text = await response.text()
    console.log("Raw API Response (first 200 chars):", text.substring(0, 200) + (text.length > 200 ? "..." : ""))

    let data
    try {
      data = JSON.parse(text)
      console.log("Parsed API Response:", data)
    } catch (e) {
      console.error("JSON parse error:", e)
      throw new Error("Invalid JSON response from API: " + text.substring(0, 100))
    }

    if (!data) {
      throw new Error("Empty response from API")
    }

    console.log("Sending success response back to popup")
    sendResponse({ success: true, data: data, text: text })
  } catch (error) {
    console.error("Queue fetch failed:", error)
    sendResponse({ success: false, error: error.message })
  }
}

async function handleUpdateStatus(request, sendResponse) {
  try {
    const { apiUrl, userId, secret, id, status } = request

    if (!apiUrl || !userId || !secret || !id || !status) {
      throw new Error("Missing required parameters for status update")
    }

    const response = await updateMessageStatus(apiUrl, userId, secret, id, status)
    console.log("Status update successful:", response)
    sendResponse({ success: true, data: response })
  } catch (error) {
    console.error("Status update failed:", error)
    sendResponse({ success: false, error: error.message })
  }
}

async function updateMessageStatus(apiUrl, userId, secret, messageId, status) {
  let url = apiUrl
  if (!url.includes("?")) {
    url += "?"
  } else if (!url.endsWith("&") && !url.endsWith("?")) {
    url += "&"
  }

  url += `method=update_status&userid=${encodeURIComponent(userId)}&secret=${encodeURIComponent(secret)}&id=${encodeURIComponent(messageId)}&status=${encodeURIComponent(status)}`

  console.log("Updating status:", { messageId, status, url })

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: {
        Accept: "*/*",
        "Cache-Control": "no-cache",
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const text = await response.text()
    console.log("Raw status update response:", text)

    let data
    try {
      data = JSON.parse(text)
    } catch (e) {
      console.error("JSON parse error:", e)
      throw new Error("Invalid JSON response from API")
    }

    return data
  } catch (error) {
    if (error.name === "TypeError" && error.message.includes("fetch")) {
      throw new Error("Network error: Unable to update status")
    }
    throw error
  }
}

chrome.runtime.onStartup.addListener(() => {
  console.log("Extension started")
})

chrome.runtime.onInstalled.addListener((details) => {
  console.log("Extension installed/updated:", details.reason)
})

// Download cloud file and convert to File object
async function handleDownloadCloudFile(request, sendResponse) {
  try {
    const { url, filename } = request

    if (!url) {
      throw new Error("Missing URL parameter")
    }

    console.log("Downloading cloud file:", url)

    const response = await fetch(url, {
      method: "GET",
      headers: {
        Accept: "*/*",
        "Cache-Control": "no-cache",
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const blob = await response.blob()
    const actualFilename = filename || extractFilenameFromUrl(url)

    // Convert blob to base64 for transfer
    const base64Data = await blobToBase64(blob)

    console.log("Cloud file downloaded successfully:", actualFilename, blob.size, "bytes")

    sendResponse({
      success: true,
      fileData: {
        name: actualFilename,
        type: blob.type,
        size: blob.size,
        data: base64Data
      }
    })
  } catch (error) {
    console.error("Cloud file download error:", error)
    sendResponse({ success: false, error: error.message })
  }
}

// Process local file (this would need to be handled differently in a real extension)
async function handleProcessLocalFile(request, sendResponse) {
  try {
    const { filename, folderPath } = request

    if (!filename) {
      throw new Error("Missing filename parameter")
    }

    console.log("Processing local file:", filename, "from folder:", folderPath)

    // Note: In a browser extension, we cannot directly access local files
    // This would need to be handled by asking the user to select the file
    // or by using a file input dialog

    sendResponse({
      success: false,
      error: "Local file access not supported in browser extension. Please use file upload instead.",
      requiresUserSelection: true,
      filename: filename
    })
  } catch (error) {
    console.error("Local file processing error:", error)
    sendResponse({ success: false, error: error.message })
  }
}

// Process multi field containing both cloud and local files
async function handleProcessMultiFiles(request, sendResponse) {
  try {
    const { multiData, folderPath } = request

    if (!multiData) {
      throw new Error("Missing multiData parameter")
    }

    console.log("Processing multi files:", multiData)

    let parsedMulti
    try {
      parsedMulti = typeof multiData === 'string' ? JSON.parse(multiData) : multiData
    } catch (parseError) {
      throw new Error("Invalid multi data format: " + parseError.message)
    }

    if (!parsedMulti.whatsapp || !Array.isArray(parsedMulti.whatsapp)) {
      throw new Error("Invalid multi data structure: missing whatsapp array")
    }

    const processedFiles = []
    const localFiles = []

    for (const item of parsedMulti.whatsapp) {
      if (!item.link) continue

      if (isCloudFile(item.link)) {
        // Download cloud file
        try {
          const downloadResult = await downloadCloudFileInternal(item.link)
          processedFiles.push({
            ...item,
            fileData: downloadResult,
            type: 'cloud'
          })
        } catch (downloadError) {
          console.error("Failed to download cloud file:", item.link, downloadError)
          processedFiles.push({
            ...item,
            error: downloadError.message,
            type: 'cloud'
          })
        }
      } else {
        // Local file - add to list for user selection
        localFiles.push({
          ...item,
          type: 'local'
        })
      }
    }

    console.log("Multi files processed:", {
      total: parsedMulti.whatsapp.length,
      cloudFiles: processedFiles.filter(f => f.type === 'cloud').length,
      localFiles: localFiles.length
    })

    sendResponse({
      success: true,
      processedFiles,
      localFiles,
      requiresLocalFileSelection: localFiles.length > 0
    })
  } catch (error) {
    console.error("Multi files processing error:", error)
    sendResponse({ success: false, error: error.message })
  }
}

// Helper functions
function isCloudFile(link) {
  return link && (link.startsWith('http://') || link.startsWith('https://'))
}

function extractFilenameFromUrl(url) {
  try {
    const urlObj = new URL(url)
    const pathname = urlObj.pathname
    const filename = pathname.split('/').pop()

    if (filename && filename.includes('.')) {
      return filename.split('?')[0] // Remove query parameters
    }

    // Fallback to a generic name based on content type
    return 'downloaded_file'
  } catch (error) {
    return 'downloaded_file'
  }
}

async function downloadCloudFileInternal(url) {
  const response = await fetch(url, {
    method: "GET",
    headers: {
      Accept: "*/*",
      "Cache-Control": "no-cache",
    },
  })

  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`)
  }

  const blob = await response.blob()
  const filename = extractFilenameFromUrl(url)
  const base64Data = await blobToBase64(blob)

  return {
    name: filename,
    type: blob.type,
    size: blob.size,
    data: base64Data
  }
}

function blobToBase64(blob) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => resolve(reader.result)
    reader.onerror = reject
    reader.readAsDataURL(blob)
  })
}
