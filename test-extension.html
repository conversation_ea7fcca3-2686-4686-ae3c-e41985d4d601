<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Extension Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #25d366;
            text-align: center;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: 500;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .step {
            margin: 15px 0;
            padding: 15px;
            border-left: 4px solid #25d366;
            background: #f8f9fa;
        }
        .step h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .code {
            font-family: 'Courier New', monospace;
            background: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            border: 1px solid #e9ecef;
        }
        button {
            background: #25d366;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        button:hover {
            background: #128c7e;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <h1>🚀 WhatsApp Automation Extension - Test Guide</h1>
    
    <div class="container">
        <h2>📋 Installation & Testing Steps</h2>
        
        <div class="step">
            <h3>Step 1: Load Extension in Chrome</h3>
            <p>1. Open Chrome and go to <code class="code">chrome://extensions/</code></p>
            <p>2. Enable "Developer mode" (toggle in top right)</p>
            <p>3. Click "Load unpacked" and select the extension folder</p>
            <p>4. The extension should appear in your extensions list</p>
        </div>

        <div class="step">
            <h3>Step 2: Open Extension Popup</h3>
            <p>1. Click the extension icon in the Chrome toolbar</p>
            <p>2. If you don't see it, click the puzzle piece icon and pin the extension</p>
            <p>3. The popup should open without any console errors</p>
        </div>

        <div class="step">
            <h3>Step 3: Test API Configuration</h3>
            <p>1. Go to the "API Bulk" tab in the extension popup</p>
            <p>2. Enter your API credentials:</p>
            <ul>
                <li><strong>API URL:</strong> Your API endpoint</li>
                <li><strong>User ID:</strong> Your user ID</li>
                <li><strong>Secret Key:</strong> Your secret key</li>
                <li><strong>Local Folder Path:</strong> Path to your local files (e.g., C:\files\)</li>
            </ul>
            <p>3. Click "Test API Connection" to verify your API works</p>
        </div>

        <div class="step">
            <h3>Step 4: Test with Sample API Response</h3>
            <p>Use this sample API response format for testing:</p>
            <div class="code" style="padding: 10px; white-space: pre-wrap; font-size: 12px;">
{
  "success": true,
  "whatsapp": [
    {
      "id": "test001",
      "mobile": "1234567890",
      "message": "Test message",
      "caption": "Test caption",
      "status": "0",
      "multi": "{\"success\":true,\"whatsapp\":[{\"message\":\"Cloud file test\",\"caption\":\"Downloaded image\",\"status\":\"1\",\"link\":\"https://picsum.photos/400/300\"},{\"message\":\"Local file test\",\"caption\":\"User selected file\",\"status\":\"1\",\"link\":\"test.jpg\"}]}"
    }
  ]
}
            </div>
        </div>

        <div class="step">
            <h3>Step 5: Test File Processing</h3>
            <p>1. Click "Fetch Message Queue" to get messages from your API</p>
            <p>2. The extension should:</p>
            <ul>
                <li>Download cloud files automatically</li>
                <li>Prompt you to select local files</li>
                <li>Create a processed queue with all files</li>
            </ul>
            <p>3. Review the queue to ensure files are properly processed</p>
        </div>

        <div class="step">
            <h3>Step 6: Test Message Sending</h3>
            <p>1. Open WhatsApp Web in another tab and log in</p>
            <p>2. Set a delay between messages (start with 10 seconds)</p>
            <p>3. Click "Start Bulk Send" to begin automation</p>
            <p>4. Monitor the progress and check for any errors</p>
        </div>
    </div>

    <div class="container">
        <h2>🔧 Troubleshooting Common Issues</h2>
        
        <div class="step">
            <h3>Console Errors</h3>
            <p>1. Open Chrome DevTools (F12)</p>
            <p>2. Check the Console tab for any JavaScript errors</p>
            <p>3. Common fixes:</p>
            <ul>
                <li>Refresh the extension popup</li>
                <li>Reload the extension in chrome://extensions/</li>
                <li>Clear browser cache and cookies</li>
            </ul>
        </div>

        <div class="step">
            <h3>API Connection Issues</h3>
            <p>1. Verify your API URL is correct and accessible</p>
            <p>2. Check that your credentials are valid</p>
            <p>3. Test the API URL directly in a browser</p>
            <p>4. Ensure the API returns valid JSON</p>
        </div>

        <div class="step">
            <h3>File Processing Issues</h3>
            <p>1. Check that cloud file URLs are publicly accessible</p>
            <p>2. Ensure local file paths are correct</p>
            <p>3. Verify file sizes are under 16MB</p>
            <p>4. Check file formats are supported</p>
        </div>

        <div class="step">
            <h3>WhatsApp Web Issues</h3>
            <p>1. Make sure you're logged into WhatsApp Web</p>
            <p>2. Refresh WhatsApp Web if needed</p>
            <p>3. Check that WhatsApp Web is up to date</p>
            <p>4. Ensure popup blockers are disabled</p>
        </div>
    </div>

    <div class="container">
        <h2>📊 Test Results</h2>
        <div id="testResults">
            <p>Run the tests above and note any issues here.</p>
        </div>
        
        <button onclick="runBasicTest()">Run Basic Test</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const resultItem = document.createElement('div');
            resultItem.className = `status ${type}`;
            resultItem.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            resultsDiv.appendChild(resultItem);
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '<p>Test results cleared.</p>';
        }

        function runBasicTest() {
            addResult('Starting basic extension test...', 'info');
            
            // Test if Chrome extension APIs are available
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                addResult('✅ Chrome extension APIs are available', 'success');
            } else {
                addResult('❌ Chrome extension APIs not available - this page needs to be opened from an extension context', 'error');
                return;
            }

            // Test extension manifest
            try {
                const manifest = chrome.runtime.getManifest();
                addResult(`✅ Extension manifest loaded: ${manifest.name} v${manifest.version}`, 'success');
            } catch (error) {
                addResult(`❌ Error loading manifest: ${error.message}`, 'error');
            }

            addResult('Basic test completed. Check the extension popup for full functionality.', 'info');
        }

        // Auto-run basic test if in extension context
        if (typeof chrome !== 'undefined' && chrome.runtime) {
            setTimeout(runBasicTest, 1000);
        }
    </script>
</body>
</html>
