# WhatsApp Automation with Multi-File Support

This Chrome extension automates WhatsApp message sending with support for both cloud and local files through API integration.

## Features

### 1. Manual Message Sending
- Send individual messages with file attachments
- Support for images, videos, documents, and audio files
- Drag & drop file upload interface

### 2. API Bulk Messaging with Multi-File Support
- Fetch message queues from your API
- Process `multi` field containing both cloud and local file references
- Automatic cloud file downloading
- Local file selection for files referenced in API
- Send messages with appropriate captions and attachments

## API Response Format

Your API should return data in this format:

```json
{
  "success": true,
  "whatsapp": [
    {
      "id": "170006",
      "user_id": "9cb65fdf72-36",
      "mobile": "919703924689",
      "message": "Main message from API",
      "caption": "Main caption text",
      "status": "0",
      "link": "https://example.com/image.jpg",
      "multi": "{\"success\":true,\"whatsapp\":[{\"message\":\"Message for first file\",\"caption\":\"Caption for first image\",\"status\":\"1\",\"link\":\"https://example.com/cloud-image.jpg\"},{\"message\":\"Message for local file\",\"caption\":\"Caption for local file\",\"status\":\"1\",\"link\":\"local-file.jpeg\"}]}"
    }
  ]
}
```

## Multi Field Processing

The `multi` field contains a JSON string with an array of items that can be:

### Cloud Files
- URLs starting with `http://` or `https://`
- Automatically downloaded by the extension
- Sent with their respective captions

### Local Files
- Simple filenames like `1.jpeg`, `2.jpeg`
- User will be prompted to select these files manually
- Matched with captions from the API

## Setup Instructions

1. **Install the Extension**
   - Load the extension in Chrome Developer Mode
   - Navigate to `chrome://extensions/`
   - Enable "Developer mode"
   - Click "Load unpacked" and select this folder

2. **Configure API Settings**
   - Open the extension popup
   - Go to the "API Bulk" tab
   - Enter your API URL, User ID, and Secret Key
   - Set the local folder path where your files are stored

3. **Test API Connection**
   - Click "Test API Connection" to verify your API works
   - Click "Open API in Browser" to see the raw response

4. **Fetch and Process Messages**
   - Click "Fetch Message Queue" to get messages from your API
   - The extension will automatically:
     - Download cloud files from URLs
     - Prompt you to select local files
     - Create a queue with all processed messages

5. **Send Messages**
   - Review the processed queue
   - Set delay between messages (5-30 seconds)
   - Click "Start Bulk Send" to begin automation
   - Monitor progress in the progress bar

## File Handling

### Supported File Types
- **Images**: JPG, PNG, GIF, WebP
- **Videos**: MP4, AVI, MOV, WebM
- **Documents**: PDF, DOC, DOCX, TXT
- **Audio**: MP3, WAV, OGG
- **Archives**: ZIP, RAR

### File Size Limits
- Maximum 16MB per file (WhatsApp limitation)

### Cloud File Processing
1. Extension downloads files from URLs in the `multi` field
2. Files are converted to the appropriate format
3. Sent with captions specified in the API response

### Local File Processing
1. Extension identifies local file references (non-URL links)
2. Prompts user to select corresponding files
3. Matches selected files with API data
4. Sends files with appropriate captions

## Usage Examples

### Example 1: Mixed Cloud and Local Files
```json
{
  "multi": "{\"success\":true,\"whatsapp\":[
    {\"message\":\"Cloud image message\",\"caption\":\"Downloaded from cloud\",\"link\":\"https://example.com/image.jpg\"},
    {\"message\":\"Local file message\",\"caption\":\"User selected file\",\"link\":\"document.pdf\"}
  ]}"
}
```

### Example 2: Multiple Local Files
```json
{
  "multi": "{\"success\":true,\"whatsapp\":[
    {\"message\":\"First local file\",\"caption\":\"Caption 1\",\"link\":\"file1.jpg\"},
    {\"message\":\"Second local file\",\"caption\":\"Caption 2\",\"link\":\"file2.pdf\"},
    {\"message\":\"Third local file\",\"caption\":\"Caption 3\",\"link\":\"file3.mp4\"}
  ]}"
}
```

## Troubleshooting

### Common Issues

1. **API Connection Failed**
   - Check your API URL, User ID, and Secret Key
   - Verify the API is accessible and returns valid JSON
   - Check browser console for detailed error messages

2. **Files Not Downloading**
   - Ensure cloud file URLs are publicly accessible
   - Check if files exceed 16MB size limit
   - Verify file formats are supported

3. **Local Files Not Found**
   - Make sure to select the correct files when prompted
   - Ensure file names match those in the API response
   - Check file permissions and accessibility

4. **WhatsApp Web Issues**
   - Ensure you're logged into WhatsApp Web
   - Refresh the WhatsApp Web page if needed
   - Check if WhatsApp Web is up to date

### Debug Information
- Enable browser console to see detailed logs
- Use the "Test API Connection" feature to verify API responses
- Check the debug area in the extension popup for API response details

## Security Notes

- API credentials are stored locally in the browser
- Files are processed locally and not sent to external servers
- Cloud files are downloaded directly from their URLs
- Local files are selected by the user and processed locally

## Quick Testing Guide

### Test with Local Server
1. **Start Test Server**:
   ```bash
   python test-server.py
   ```
   Server will run at `http://localhost:8000`

2. **Configure Extension**:
   - API URL: `http://localhost:8000/api`
   - User ID: `test-user`
   - Secret Key: `test-secret`
   - Local Folder Path: `C:\your\files\folder`

3. **Test the Flow**:
   - Click "Test API Connection" (should show success)
   - Click "Fetch Message Queue"
   - Extension will download cloud files automatically
   - You'll see a dialog to select local files
   - Select files when prompted
   - Review the processed queue

### Expected Behavior After Selecting Folder

When you click "Fetch Message Queue":

1. **Automatic Processing**:
   - ✅ Cloud files download automatically
   - ✅ A dialog appears asking you to select local files
   - ✅ Queue shows processed items with file types

2. **Local File Dialog**:
   - 📁 Lists the local files referenced in API
   - 📂 Allows you to select corresponding files
   - ✅ Matches selected files with API data
   - 📊 Updates queue with file information

3. **Queue Display**:
   - ☁️ Cloud files (downloaded automatically)
   - 📁 Local files (user selected)
   - 📄 Text-only (if files were skipped)
   - 📊 Summary showing file type counts

### If Nothing Happens After Selecting Folder

**Check These Items**:

1. **Browser Console** (F12):
   ```
   Look for error messages or logs starting with:
   - "Processing queue with multi files support..."
   - "Processing multi field for item..."
   - "Handling local file selection for..."
   ```

2. **API Response**:
   - Verify API returns valid JSON
   - Check that `multi` field contains valid JSON string
   - Ensure local file references exist in the data

3. **Extension State**:
   - Refresh the extension popup
   - Reload the extension in chrome://extensions/
   - Check if WhatsApp Web is loaded

## Support

For issues or questions:
1. Check the browser console for error messages
2. Verify your API response format matches the expected structure
3. Test with the provided test server first
4. Ensure WhatsApp Web is properly loaded and logged in
