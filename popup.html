<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 380px;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      background: #f5f5f5;
      color: #333;
      font-size: 13px;
    }

    .container {
      background: white;
      margin: 0;
      border-radius: 0;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .header {
      text-align: center;
      padding: 15px;
      background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
      color: white;
      margin: 0;
      border-radius: 0;
    }

    .header h1 {
      margin: 0;
      color: white;
      font-size: 18px;
      font-weight: 600;
    }

    .header p {
      margin: 4px 0 0 0;
      color: rgba(255,255,255,0.9);
      font-size: 12px;
    }

    .tabs {
      display: flex;
      margin: 0;
      background: #f8f9fa;
      border-bottom: 2px solid #e9ecef;
    }

    .tab {
      flex: 1;
      padding: 12px 8px;
      text-align: center;
      background: #f8f9fa;
      border: none;
      cursor: pointer;
      font-size: 13px;
      font-weight: 500;
      color: #666;
      transition: all 0.2s ease;
      border-bottom: 2px solid transparent;
    }

    .tab.active {
      background: white;
      color: #25d366;
      border-bottom: 2px solid #25d366;
    }

    .tab:hover:not(.active) {
      background: #e9ecef;
      color: #333;
    }

    .tab-content {
      display: none;
      padding: 15px;
      background: white;
    }

    .tab-content.active {
      display: block;
    }

    .form-group {
      margin-bottom: 15px;
      background: #f8f9fa;
      padding: 12px;
      border-radius: 6px;
      border: 1px solid #e9ecef;
    }

    label {
      display: block;
      margin-bottom: 6px;
      font-weight: 500;
      color: #333;
      font-size: 13px;
    }

    /* Folder Path Section */
    .folder-path-section {
      background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
      padding: 15px;
      border-radius: 8px;
      border: 2px solid #e17055;
      margin: 15px 0;
      position: relative;
    }

    .folder-path-section::before {
      content: "⚠️ REQUIRED SETUP";
      position: absolute;
      top: -1px;
      left: -1px;
      right: -1px;
      background: #e17055;
      color: white;
      text-align: center;
      padding: 4px;
      font-weight: bold;
      font-size: 11px;
      border-radius: 6px 6px 0 0;
    }

    .folder-path-section label {
      color: #2d3436 !important;
      font-weight: 600 !important;
      font-size: 13px !important;
      margin: 15px 0 8px 0 !important;
    }

    .folder-path-input {
      padding: 8px 10px !important;
      border: 1px solid #ddd !important;
      border-radius: 4px !important;
      font-size: 12px !important;
      font-family: monospace !important;
      background: white !important;
    }

    .folder-path-input:focus {
      outline: none !important;
      border-color: #25d366 !important;
      box-shadow: 0 0 0 2px rgba(37, 211, 102, 0.2) !important;
    }

    input, textarea, select {
      width: 100%;
      padding: 8px 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 13px;
      box-sizing: border-box;
      background: white;
      transition: all 0.2s ease;
      font-family: inherit;
    }

    input:focus, textarea:focus, select:focus {
      outline: none;
      border-color: #25d366;
      box-shadow: 0 0 0 2px rgba(37, 211, 102, 0.2);
    }

    input::placeholder {
      color: #999;
      font-size: 12px;
    }

    textarea {
      height: 60px;
      resize: vertical;
    }

    button {
      width: 100%;
      padding: 10px 15px;
      background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
      color: white;
      border: none;
      border-radius: 6px;
      font-size: 13px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      margin-bottom: 10px;
    }

    button:hover:not(:disabled) {
      background: linear-gradient(135deg, #128c7e 0%, #25d366 100%);
    }

    button:disabled {
      background: #ccc;
      cursor: not-allowed;
    }

    .secondary-btn {
      background: #6c757d;
    }

    .secondary-btn:hover:not(:disabled) {
      background: #5a6268;
    }

    .status {
      padding: 10px;
      border-radius: 5px;
      margin: 10px 0;
      font-weight: 500;
      display: none;
    }

    .status.success {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }

    .status.error {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }

    .status.info {
      background: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }

    .queue-container {
      display: none;
      margin-top: 20px;
    }

    .queue-list {
      max-height: 300px;
      overflow-y: auto;
      border: 1px solid #ddd;
      border-radius: 5px;
      padding: 10px;
      background: #f8f9fa;
    }

    .queue-item {
      background: white;
      padding: 12px;
      margin-bottom: 10px;
      border-radius: 5px;
      border: 1px solid #e9ecef;
      font-size: 13px;
      line-height: 1.4;
    }

    .queue-item:last-child {
      margin-bottom: 0;
    }

    .progress-container {
      display: none;
      margin: 15px 0;
    }

    .progress-bar {
      width: 100%;
      height: 25px;
      background: #e9ecef;
      border-radius: 5px;
      overflow: hidden;
      position: relative;
    }

    .progress-fill {
      height: 100%;
      background: #25d366;
      transition: width 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: 500;
      font-size: 12px;
    }

    .progress-text {
      text-align: center;
      margin-top: 5px;
      font-size: 12px;
      color: #666;
    }

    .api-credentials {
      background: #f8f9fa;
      padding: 15px;
      border-radius: 5px;
      margin-bottom: 15px;
      border: 1px solid #e9ecef;
    }

    .api-credentials h3 {
      margin: 0 0 10px 0;
      color: #333;
      font-size: 16px;
    }

    .bulk-controls {
      display: flex;
      gap: 10px;
      margin: 15px 0;
    }

    .bulk-controls button {
      flex: 1;
      margin-bottom: 0;
    }

    #stopBulk {
      background: #dc3545;
      display: none;
    }

    #stopBulk:hover:not(:disabled) {
      background: #c82333;
    }

    .delay-control {
      margin: 15px 0;
    }

    .test-buttons {
      display: flex;
      gap: 10px;
      margin: 10px 0;
    }

    .test-buttons button {
      flex: 1;
      margin-bottom: 0;
      font-size: 12px;
      padding: 8px;
    }

    /* File upload styles */
    .file-upload-container {
      border: 2px dashed #ddd;
      border-radius: 5px;
      padding: 20px;
      text-align: center;
      background: #f8f9fa;
      margin-bottom: 15px;
      transition: all 0.3s ease;
    }

    .file-upload-container:hover {
      border-color: #25d366;
      background: #f0f8f0;
    }

    .file-upload-container.dragover {
      border-color: #25d366;
      background: #e8f5e8;
    }

    .file-input {
      display: none;
    }

    .file-upload-button {
      background: #6c757d;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      margin-bottom: 0;
      width: auto;
    }

    .file-upload-button:hover {
      background: #5a6268;
    }

    .file-preview {
      margin-top: 10px;
      padding: 10px;
      background: white;
      border: 1px solid #ddd;
      border-radius: 5px;
      display: none;
    }

    .file-preview.show {
      display: block;
    }

    .file-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px;
      margin: 5px 0;
      background: #f8f9fa;
      border-radius: 4px;
      border: 1px solid #e9ecef;
    }

    .file-info {
      display: flex;
      align-items: center;
      flex: 1;
    }

    .file-icon {
      width: 24px;
      height: 24px;
      margin-right: 8px;
      background: #25d366;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 12px;
      font-weight: bold;
    }

    .file-details {
      flex: 1;
    }

    .file-name {
      font-weight: 500;
      font-size: 13px;
      color: #333;
      margin-bottom: 2px;
    }

    .file-size {
      font-size: 11px;
      color: #666;
    }

    .file-remove {
      background: #dc3545;
      color: white;
      border: none;
      padding: 4px 8px;
      border-radius: 3px;
      cursor: pointer;
      font-size: 11px;
      width: auto;
      margin: 0;
    }

    .file-remove:hover {
      background: #c82333;
    }

    .file-type-filter {
      margin-bottom: 10px;
    }

    .file-type-buttons {
      display: flex;
      gap: 5px;
      flex-wrap: wrap;
    }

    .file-type-btn {
      padding: 4px 8px;
      background: #e9ecef;
      border: 1px solid #ddd;
      border-radius: 3px;
      cursor: pointer;
      font-size: 11px;
      color: #666;
      width: auto;
      margin: 0;
    }

    .file-type-btn.active {
      background: #25d366;
      color: white;
      border-color: #25d366;
    }

    .file-type-btn:hover:not(.active) {
      background: #dee2e6;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🚀 WhatsApp Automation</h1>
      <p>Professional messaging with media support</p>
    </div>

  <div class="tabs">
    <button class="tab active" data-tab="manual">Manual</button>
    <button class="tab" data-tab="api">API Bulk</button>
  </div>

  <div id="manual-tab" class="tab-content active">
    <div class="form-group">
      <label for="phoneNumber">Phone Number (with country code):</label>
      <input type="text" id="phoneNumber" placeholder="e.g., 919876543210">
    </div>

    <div class="form-group">
      <label for="message">Message:</label>
      <textarea id="message" placeholder="Enter your message here..."></textarea>
    </div>



    <button id="trigger">Send Message</button>
    
    <div id="status" class="status"></div>
  </div>

  <div id="api-tab" class="tab-content">
    <div class="api-credentials">
      <h3>API Configuration</h3>
      <div class="form-group">
        <label for="apiUrl">API URL:</label>
        <input type="text" id="apiUrl" placeholder="https://your-api-url.com/api">
      </div>

      <div class="form-group">
        <label for="userId">User ID:</label>
        <input type="text" id="userId" placeholder="Your user ID">
      </div>

      <div class="form-group">
        <label for="secret">Secret Key:</label>
        <input type="password" id="secret" placeholder="Your secret key">
      </div>

      <!-- Folder Path Section -->
      <div class="form-group folder-path-section">
        <label for="localFolderPath">📁 Local Files Folder Path</label>

        <div style="background: rgba(255,255,255,0.9); padding: 12px; border-radius: 6px; margin-bottom: 12px;">
          <div style="color: #333; font-size: 12px; font-weight: 600; margin-bottom: 8px;">
            📋 Enter the folder path containing your local files (1.jpeg, 2.jpeg, etc.)
          </div>
          <div style="color: #666; font-size: 11px; line-height: 1.4;">
            <strong>Examples:</strong><br>
            • <code style="background: #f0f0f0; padding: 2px 4px; border-radius: 3px;">C:\MyFiles\</code><br>
            • <code style="background: #f0f0f0; padding: 2px 4px; border-radius: 3px;">D:\Documents\Images\</code>
          </div>
        </div>

        <div style="display: flex; gap: 8px; align-items: center;">
          <input type="text" id="localFolderPath" class="folder-path-input"
                 placeholder="Enter folder path (e.g., C:\MyFiles\)"
                 style="flex: 1;">
          <button type="button" id="testFolderBtn"
                  style="padding: 8px 12px; background: #25d366; color: white; border: none; border-radius: 4px; cursor: pointer; font-weight: 500; font-size: 12px; min-width: 80px;">
            📂 Test
          </button>
        </div>
      </div>
    </div>





    <button id="fetchQueue">Fetch Message Queue</button>

    <div class="delay-control">
      <label for="delay">Delay between messages:</label>
      <select id="delay">
        <option value="5">5 seconds</option>
        <option value="10" selected>10 seconds</option>
        <option value="15">15 seconds</option>
        <option value="20">20 seconds</option>
        <option value="30">30 seconds</option>
      </select>
    </div>

    <div class="bulk-controls">
      <button id="startBulk" disabled>Start Bulk Send</button>
      <button id="stopBulk">Stop Sending</button>
    </div>

    <div id="progress-container" class="progress-container">
      <div class="progress-bar">
        <div id="progressBar" class="progress-fill" style="width: 0%;">0%</div>
      </div>
      <div id="progressText" class="progress-text">Ready to start...</div>
    </div>

    <div id="queueContainer" class="queue-container">
      <h3>Message Queue:</h3>
      <div id="queueList" class="queue-list"></div>
    </div>

    <div id="status" class="status"></div>
  </div>
  </div>

  <script src="popup.js"></script>
</body>
</html>
