<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 400px;
      padding: 20px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
    }

    .header {
      text-align: center;
      margin-bottom: 20px;
      padding-bottom: 15px;
      border-bottom: 2px solid #e9ecef;
    }

    .header h1 {
      margin: 0;
      color: #25d366;
      font-size: 24px;
    }

    .header p {
      margin: 5px 0 0 0;
      color: #666;
      font-size: 14px;
    }

    .tabs {
      display: flex;
      margin-bottom: 20px;
      border-bottom: 1px solid #ddd;
    }

    .tab {
      flex: 1;
      padding: 12px;
      text-align: center;
      background: #f8f9fa;
      border: none;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      color: #666;
      transition: all 0.3s ease;
    }

    .tab:first-child {
      border-radius: 5px 0 0 0;
    }

    .tab:last-child {
      border-radius: 0 5px 0 0;
    }

    .tab.active {
      background: #25d366;
      color: white;
    }

    .tab:hover:not(.active) {
      background: #e9ecef;
    }

    .tab-content {
      display: none;
    }

    .tab-content.active {
      display: block;
    }

    .form-group {
      margin-bottom: 15px;
    }

    label {
      display: block;
      margin-bottom: 5px;
      font-weight: 500;
      color: #333;
    }

    input, textarea, select {
      width: 100%;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 5px;
      font-size: 14px;
      box-sizing: border-box;
    }

    textarea {
      height: 80px;
      resize: vertical;
    }

    button {
      width: 100%;
      padding: 12px;
      background: #25d366;
      color: white;
      border: none;
      border-radius: 5px;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      transition: background-color 0.3s ease;
      margin-bottom: 10px;
    }

    button:hover:not(:disabled) {
      background: #128c7e;
    }

    button:disabled {
      background: #ccc;
      cursor: not-allowed;
    }

    .secondary-btn {
      background: #6c757d;
    }

    .secondary-btn:hover:not(:disabled) {
      background: #5a6268;
    }

    .status {
      padding: 10px;
      border-radius: 5px;
      margin: 10px 0;
      font-weight: 500;
      display: none;
    }

    .status.success {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }

    .status.error {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }

    .status.info {
      background: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }

    .queue-container {
      display: none;
      margin-top: 20px;
    }

    .queue-list {
      max-height: 300px;
      overflow-y: auto;
      border: 1px solid #ddd;
      border-radius: 5px;
      padding: 10px;
      background: #f8f9fa;
    }

    .queue-item {
      background: white;
      padding: 12px;
      margin-bottom: 10px;
      border-radius: 5px;
      border: 1px solid #e9ecef;
      font-size: 13px;
      line-height: 1.4;
    }

    .queue-item:last-child {
      margin-bottom: 0;
    }

    .progress-container {
      display: none;
      margin: 15px 0;
    }

    .progress-bar {
      width: 100%;
      height: 25px;
      background: #e9ecef;
      border-radius: 5px;
      overflow: hidden;
      position: relative;
    }

    .progress-fill {
      height: 100%;
      background: #25d366;
      transition: width 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: 500;
      font-size: 12px;
    }

    .progress-text {
      text-align: center;
      margin-top: 5px;
      font-size: 12px;
      color: #666;
    }

    .api-credentials {
      background: #f8f9fa;
      padding: 15px;
      border-radius: 5px;
      margin-bottom: 15px;
      border: 1px solid #e9ecef;
    }

    .api-credentials h3 {
      margin: 0 0 10px 0;
      color: #333;
      font-size: 16px;
    }

    .bulk-controls {
      display: flex;
      gap: 10px;
      margin: 15px 0;
    }

    .bulk-controls button {
      flex: 1;
      margin-bottom: 0;
    }

    #stopBulk {
      background: #dc3545;
      display: none;
    }

    #stopBulk:hover:not(:disabled) {
      background: #c82333;
    }

    .delay-control {
      margin: 15px 0;
    }

    .test-buttons {
      display: flex;
      gap: 10px;
      margin: 10px 0;
    }

    .test-buttons button {
      flex: 1;
      margin-bottom: 0;
      font-size: 12px;
      padding: 8px;
    }

    /* File upload styles */
    .file-upload-container {
      border: 2px dashed #ddd;
      border-radius: 5px;
      padding: 20px;
      text-align: center;
      background: #f8f9fa;
      margin-bottom: 15px;
      transition: all 0.3s ease;
    }

    .file-upload-container:hover {
      border-color: #25d366;
      background: #f0f8f0;
    }

    .file-upload-container.dragover {
      border-color: #25d366;
      background: #e8f5e8;
    }

    .file-input {
      display: none;
    }

    .file-upload-button {
      background: #6c757d;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      margin-bottom: 0;
      width: auto;
    }

    .file-upload-button:hover {
      background: #5a6268;
    }

    .file-preview {
      margin-top: 10px;
      padding: 10px;
      background: white;
      border: 1px solid #ddd;
      border-radius: 5px;
      display: none;
    }

    .file-preview.show {
      display: block;
    }

    .file-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px;
      margin: 5px 0;
      background: #f8f9fa;
      border-radius: 4px;
      border: 1px solid #e9ecef;
    }

    .file-info {
      display: flex;
      align-items: center;
      flex: 1;
    }

    .file-icon {
      width: 24px;
      height: 24px;
      margin-right: 8px;
      background: #25d366;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 12px;
      font-weight: bold;
    }

    .file-details {
      flex: 1;
    }

    .file-name {
      font-weight: 500;
      font-size: 13px;
      color: #333;
      margin-bottom: 2px;
    }

    .file-size {
      font-size: 11px;
      color: #666;
    }

    .file-remove {
      background: #dc3545;
      color: white;
      border: none;
      padding: 4px 8px;
      border-radius: 3px;
      cursor: pointer;
      font-size: 11px;
      width: auto;
      margin: 0;
    }

    .file-remove:hover {
      background: #c82333;
    }

    .file-type-filter {
      margin-bottom: 10px;
    }

    .file-type-buttons {
      display: flex;
      gap: 5px;
      flex-wrap: wrap;
    }

    .file-type-btn {
      padding: 4px 8px;
      background: #e9ecef;
      border: 1px solid #ddd;
      border-radius: 3px;
      cursor: pointer;
      font-size: 11px;
      color: #666;
      width: auto;
      margin: 0;
    }

    .file-type-btn.active {
      background: #25d366;
      color: white;
      border-color: #25d366;
    }

    .file-type-btn:hover:not(.active) {
      background: #dee2e6;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>WhatsApp Automation</h1>
    <p>Send messages with media support</p>
  </div>

  <div class="tabs">
    <button class="tab active" data-tab="manual">Manual</button>
    <button class="tab" data-tab="api">API Bulk</button>
  </div>

  <div id="manual-tab" class="tab-content active">
    <div class="form-group">
      <label for="phoneNumber">Phone Number (with country code):</label>
      <input type="text" id="phoneNumber" placeholder="e.g., 919876543210">
    </div>

    <div class="form-group">
      <label for="message">Message:</label>
      <textarea id="message" placeholder="Enter your message here..."></textarea>
    </div>

    <!-- File Upload Section -->
    <div class="form-group">
      <label>Attach Files (Optional):</label>
      
      <div class="file-type-filter">
        <div class="file-type-buttons">
          <button class="file-type-btn active" data-type="all">All</button>
          <button class="file-type-btn" data-type="image">Images</button>
          <button class="file-type-btn" data-type="document">Documents</button>
          <button class="file-type-btn" data-type="video">Videos</button>
          <button class="file-type-btn" data-type="audio">Audio</button>
        </div>
      </div>

      <div class="file-upload-container" id="fileUploadContainer">
        <div>
          <p style="margin: 0 0 10px 0; color: #666;">
            📎 Drag & drop files here or click to browse
          </p>
          <button type="button" class="file-upload-button" id="fileUploadButton">
            Choose Files
          </button>
          <input type="file" id="fileInput" class="file-input" multiple 
                 accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt,.zip,.rar">
          <p style="margin: 10px 0 0 0; font-size: 11px; color: #999;">
            Supported: Images, Videos, Audio, Documents (Max 16MB each)
          </p>
        </div>
      </div>

      <div class="file-preview" id="filePreview">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
          <strong style="font-size: 13px;">Selected Files:</strong>
          <button type="button" class="file-remove" id="clearAllFiles" style="font-size: 11px;">
            Clear All
          </button>
        </div>
        <div id="fileList"></div>
      </div>
    </div>

    <button id="trigger">Send Message</button>
    
    <div id="status" class="status"></div>
  </div>

  <div id="api-tab" class="tab-content">
    <div class="api-credentials">
      <h3>API Configuration</h3>
      <div class="form-group">
        <label for="apiUrl">API URL:</label>
        <input type="text" id="apiUrl" placeholder="https://your-api-url.com/api">
      </div>

      <div class="form-group">
        <label for="userId">User ID:</label>
        <input type="text" id="userId" placeholder="Your user ID">
      </div>

      <div class="form-group">
        <label for="secret">Secret Key:</label>
        <input type="password" id="secret" placeholder="Your secret key">
      </div>

      <div class="form-group">
        <label for="localFolderPath">Local Files Folder Path:</label>
        <input type="text" id="localFolderPath" placeholder="C:\path\to\your\files\folder">
        <small style="color: #666; font-size: 12px; display: block; margin-top: 5px;">
          📁 Folder containing local files referenced in API (e.g., 1.jpeg, 2.jpeg)
        </small>
      </div>
    </div>

    <div class="test-buttons">
      <button id="testApi" class="secondary-btn">Test API</button>
      <button id="openBrowser" class="secondary-btn">Open in Browser</button>
    </div>

    <!-- File Upload Section for API Bulk -->
    <div class="form-group">
      <label>Attach Files for Bulk Send (Optional):</label>
      
      <div class="file-type-filter">
        <div class="file-type-buttons">
          <button class="file-type-btn active" data-type="all" id="apiBulkFileTypeAll">All</button>
          <button class="file-type-btn" data-type="image" id="apiBulkFileTypeImage">Images</button>
          <button class="file-type-btn" data-type="document" id="apiBulkFileTypeDocument">Documents</button>
          <button class="file-type-btn" data-type="video" id="apiBulkFileTypeVideo">Videos</button>
          <button class="file-type-btn" data-type="audio" id="apiBulkFileTypeAudio">Audio</button>
        </div>
      </div>

      <div class="file-upload-container" id="apiBulkFileUploadContainer">
        <div>
          <p style="margin: 0 0 10px 0; color: #666;">
            📎 Drag & drop files here or click to browse
          </p>
          <button type="button" class="file-upload-button" id="apiBulkFileUploadButton">
            Choose Files
          </button>
          <input type="file" id="apiBulkFileInput" class="file-input" multiple 
                 accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt,.zip,.rar">
          <p style="margin: 10px 0 0 0; font-size: 11px; color: #999;">
            These files will be sent with each message in the queue (Max 16MB each)
          </p>
        </div>
      </div>

      <div class="file-preview" id="apiBulkFilePreview">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
          <strong style="font-size: 13px;">Bulk Send Files:</strong>
          <button type="button" class="file-remove" id="apiBulkClearAllFiles" style="font-size: 11px;">
            Clear All
          </button>
        </div>
        <div id="apiBulkFileList"></div>
      </div>
    </div>

    <button id="fetchQueue">Fetch Message Queue</button>

    <div class="delay-control">
      <label for="delay">Delay between messages:</label>
      <select id="delay">
        <option value="5">5 seconds</option>
        <option value="10" selected>10 seconds</option>
        <option value="15">15 seconds</option>
        <option value="20">20 seconds</option>
        <option value="30">30 seconds</option>
      </select>
    </div>

    <div class="bulk-controls">
      <button id="startBulk" disabled>Start Bulk Send</button>
      <button id="stopBulk">Stop Sending</button>
    </div>

    <div id="progress-container" class="progress-container">
      <div class="progress-bar">
        <div id="progressBar" class="progress-fill" style="width: 0%;">0%</div>
      </div>
      <div id="progressText" class="progress-text">Ready to start...</div>
    </div>

    <div id="queueContainer" class="queue-container">
      <h3>Message Queue:</h3>
      <div id="queueList" class="queue-list"></div>
    </div>

    <div id="status" class="status"></div>
  </div>

  <script src="popup.js"></script>
</body>
</html>
