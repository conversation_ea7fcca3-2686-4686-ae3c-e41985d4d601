{"success": true, "whatsapp": [{"id": "test001", "user_id": "test-user", "mobile": "1234567890", "message": "Testing multi-file functionality", "caption": "Main caption", "status": "0", "link": "https://picsum.photos/400/300?random=1", "multi": "{\"success\":true,\"whatsapp\":[{\"message\":\"Cloud image message\",\"caption\":\"This image will be downloaded automatically\",\"status\":\"1\",\"link\":\"https://picsum.photos/600/400?random=2\"},{\"message\":\"Local file message 1\",\"caption\":\"Please select the first local file\",\"status\":\"1\",\"link\":\"document1.pdf\"},{\"message\":\"Another cloud image\",\"caption\":\"Another automatic download\",\"status\":\"1\",\"link\":\"https://picsum.photos/500/350?random=3\"},{\"message\":\"Local file message 2\",\"caption\":\"Please select the second local file\",\"status\":\"1\",\"link\":\"image2.jpg\"},{\"message\":\"Local file message 3\",\"caption\":\"Please select the third local file\",\"status\":\"1\",\"link\":\"video.mp4\"}]}"}, {"id": "test002", "user_id": "test-user", "mobile": "0987654321", "message": "Testing only cloud files", "caption": "All cloud files", "status": "0", "link": "https://picsum.photos/300/200?random=4", "multi": "{\"success\":true,\"whatsapp\":[{\"message\":\"First cloud file\",\"caption\":\"Cloud file 1\",\"status\":\"1\",\"link\":\"https://picsum.photos/700/500?random=5\"},{\"message\":\"Second cloud file\",\"caption\":\"Cloud file 2\",\"status\":\"1\",\"link\":\"https://picsum.photos/800/600?random=6\"}]}"}, {"id": "test003", "user_id": "test-user", "mobile": "1122334455", "message": "Testing only local files", "caption": "All local files", "status": "0", "multi": "{\"success\":true,\"whatsapp\":[{\"message\":\"Local document\",\"caption\":\"Important document\",\"status\":\"1\",\"link\":\"contract.pdf\"},{\"message\":\"Local image\",\"caption\":\"Photo from event\",\"status\":\"1\",\"link\":\"event-photo.jpg\"},{\"message\":\"Local spreadsheet\",\"caption\":\"Monthly report\",\"status\":\"1\",\"link\":\"report.xlsx\"}]}"}, {"id": "test004", "user_id": "test-user", "mobile": "5566778899", "message": "Regular message without multi field", "caption": "Simple message", "status": "0", "link": "https://picsum.photos/400/400?random=7"}]}