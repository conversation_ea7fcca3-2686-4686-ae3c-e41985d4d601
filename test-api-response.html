<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Automation - API Response Tester</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #25d366;
            text-align: center;
        }
        h2 {
            color: #333;
            border-bottom: 2px solid #25d366;
            padding-bottom: 10px;
        }
        .json-container {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            overflow-x: auto;
        }
        pre {
            margin: 0;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .example {
            margin: 20px 0;
        }
        .note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .code {
            font-family: 'Courier New', monospace;
            background: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>🚀 WhatsApp Automation - API Response Format Guide</h1>
    
    <div class="container">
        <h2>📋 Expected API Response Structure</h2>
        <p>Your API should return a JSON response in the following format:</p>
        
        <div class="json-container">
            <pre id="main-example"></pre>
        </div>
        
        <div class="note success">
            <strong>✅ Key Points:</strong>
            <ul>
                <li>The <code>multi</code> field contains a JSON string (not an object)</li>
                <li>Cloud files use full URLs (http/https)</li>
                <li>Local files use simple filenames</li>
                <li>Each item in the multi array can have its own message and caption</li>
            </ul>
        </div>
    </div>

    <div class="container">
        <h2>🔍 Multi Field Structure</h2>
        <p>The <code>multi</code> field should contain a JSON string with this structure:</p>
        
        <div class="json-container">
            <pre id="multi-example"></pre>
        </div>
        
        <div class="note info">
            <strong>ℹ️ File Type Detection:</strong>
            <ul>
                <li><strong>Cloud Files:</strong> URLs starting with <code>http://</code> or <code>https://</code></li>
                <li><strong>Local Files:</strong> Simple filenames like <code>image.jpg</code>, <code>document.pdf</code></li>
            </ul>
        </div>
    </div>

    <div class="container">
        <h2>📁 File Processing Examples</h2>
        
        <h3>Example 1: Mixed Cloud and Local Files</h3>
        <div class="json-container">
            <pre id="mixed-example"></pre>
        </div>
        
        <h3>Example 2: Only Cloud Files</h3>
        <div class="json-container">
            <pre id="cloud-example"></pre>
        </div>
        
        <h3>Example 3: Only Local Files</h3>
        <div class="json-container">
            <pre id="local-example"></pre>
        </div>
    </div>

    <div class="container">
        <h2>⚙️ API Parameters</h2>
        <p>Your API endpoint should accept these parameters:</p>
        
        <table>
            <tr>
                <th>Parameter</th>
                <th>Description</th>
                <th>Example</th>
            </tr>
            <tr>
                <td><code>method</code></td>
                <td>API method name</td>
                <td><code>list_whatsapp_l</code></td>
            </tr>
            <tr>
                <td><code>userid</code></td>
                <td>Your user ID</td>
                <td><code>your-user-id</code></td>
            </tr>
            <tr>
                <td><code>secret</code></td>
                <td>Your secret key</td>
                <td><code>your-secret-key</code></td>
            </tr>
        </table>
        
        <div class="note warning">
            <strong>⚠️ Security Note:</strong> Keep your API credentials secure and use HTTPS for your API endpoints.
        </div>
    </div>

    <div class="container">
        <h2>🧪 Test Your API</h2>
        <p>Use this URL format to test your API manually:</p>
        <div class="json-container">
            <code>https://your-api-url.com/api?method=list_whatsapp_l&userid=YOUR_USER_ID&secret=YOUR_SECRET</code>
        </div>
        
        <div class="note info">
            <strong>💡 Testing Tips:</strong>
            <ul>
                <li>Test your API URL in a browser first</li>
                <li>Verify the JSON response format</li>
                <li>Check that cloud file URLs are accessible</li>
                <li>Ensure local filenames match your actual files</li>
            </ul>
        </div>
    </div>

    <script>
        // Sample API response data
        const mainExample = {
            "success": true,
            "whatsapp": [
                {
                    "id": "170006",
                    "user_id": "9cb65fdf72-36",
                    "mobile": "919703924689",
                    "message": "Main message from API",
                    "caption": "Main caption text",
                    "status": "0",
                    "link": "https://www.shutterstock.com/image-vector/moscow-russia-august-18-2021-260nw-2300430413.jpg",
                    "multi": "{\"success\":true,\"whatsapp\":[{\"message\":\"Cloud image message\",\"caption\":\"Caption for cloud image\",\"status\":\"1\",\"link\":\"https://www.shutterstock.com/image-illustration/human-body-organs-internal-parts-600nw-2426287857.jpg\"},{\"message\":\"Local file message\",\"caption\":\"Caption for local file\",\"status\":\"1\",\"link\":\"local-document.pdf\"}]}"
                }
            ]
        };

        const multiExample = {
            "success": true,
            "whatsapp": [
                {
                    "message": "First file message",
                    "caption": "Caption for first file",
                    "status": "1",
                    "link": "https://example.com/image.jpg"
                },
                {
                    "message": "Second file message", 
                    "caption": "Caption for second file",
                    "status": "1",
                    "link": "local-file.pdf"
                }
            ]
        };

        const mixedExample = {
            "multi": "{\"success\":true,\"whatsapp\":[{\"message\":\"Downloaded image\",\"caption\":\"This image will be downloaded automatically\",\"status\":\"1\",\"link\":\"https://picsum.photos/800/600\"},{\"message\":\"User selected document\",\"caption\":\"User will select this file manually\",\"status\":\"1\",\"link\":\"report.pdf\"},{\"message\":\"Another cloud image\",\"caption\":\"Another automatic download\",\"status\":\"1\",\"link\":\"https://picsum.photos/400/300\"}]}"
        };

        const cloudExample = {
            "multi": "{\"success\":true,\"whatsapp\":[{\"message\":\"Image 1\",\"caption\":\"First cloud image\",\"status\":\"1\",\"link\":\"https://picsum.photos/800/600?random=1\"},{\"message\":\"Image 2\",\"caption\":\"Second cloud image\",\"status\":\"1\",\"link\":\"https://picsum.photos/800/600?random=2\"},{\"message\":\"Image 3\",\"caption\":\"Third cloud image\",\"status\":\"1\",\"link\":\"https://picsum.photos/800/600?random=3\"}]}"
        };

        const localExample = {
            "multi": "{\"success\":true,\"whatsapp\":[{\"message\":\"Local image 1\",\"caption\":\"First local file\",\"status\":\"1\",\"link\":\"photo1.jpg\"},{\"message\":\"Local document\",\"caption\":\"Important document\",\"status\":\"1\",\"link\":\"contract.pdf\"},{\"message\":\"Local video\",\"caption\":\"Presentation video\",\"status\":\"1\",\"link\":\"presentation.mp4\"}]}"
        };

        // Display examples
        document.getElementById('main-example').textContent = JSON.stringify(mainExample, null, 2);
        document.getElementById('multi-example').textContent = JSON.stringify(multiExample, null, 2);
        document.getElementById('mixed-example').textContent = JSON.stringify(mixedExample, null, 2);
        document.getElementById('cloud-example').textContent = JSON.stringify(cloudExample, null, 2);
        document.getElementById('local-example').textContent = JSON.stringify(localExample, null, 2);
    </script>
</body>
</html>
