# 📁 Folder-Based File Selection Testing Guide

## ✅ **What's Fixed**

The extension now uses **folder selection** instead of individual file selection:

1. **📁 Choose Folder Button** - Select entire folder containing your files
2. **🔍 Automatic File Matching** - Finds files by name from the `multi` field
3. **📝 Correct Captions** - Uses captions from `multi` field, not main message
4. **📎 All Files Attached** - Processes every file referenced in the API

## 🚀 **How to Test**

### Step 1: Prepare Test Files
Create a folder with these files (matching your API response):
```
test-files/
├── 1.jpeg
├── 2.jpeg
└── (any other files referenced in your API)
```

### Step 2: Start Test Server
```bash
python test-server.py
```
Server runs at: `http://localhost:8000`

### Step 3: Configure Extension
1. Open extension popup
2. Go to "API Bulk" tab
3. Enter:
   - **API URL**: `http://localhost:8000/api`
   - **User ID**: `test-user`
   - **Secret Key**: `test-secret`
4. Click **"📁 Choose Folder"** button
5. Select your `test-files` folder

### Step 4: Test the Flow
1. Click **"Fetch Message Queue"**
2. Watch the console for processing logs
3. Check the queue display for processed files

## 📊 **Expected Results**

### Queue Processing
For each API item with `multi` field, you should see:

**Cloud Files (URLs)**:
- ☁️ Automatically downloaded
- Caption: "caption for first image", "caption for second image", etc.
- Message: "loopmessage1"

**Local Files (1.jpeg, 2.jpeg)**:
- 📁 Found in your selected folder
- Caption: "caption for first image" 
- Message: "looplocalmessage1"

### Queue Display
```
📊 Queue Summary: 15 total messages
📝 Regular: 0 | ☁️ Cloud files: 9 | 📁 Local files: 6 | 📄 Text-only: 0

1. ☁️ To: 919703924689
   📝 Message: loopmessage1
   💬 Caption: caption for first image
   📎 Files: downloaded_file (Cloud file)

2. 📁 To: 919703924689  
   📝 Message: looplocalmessage1
   💬 Caption: caption for first image
   📎 Files: 1.jpeg (Local file) - Selected: 1.jpeg
```

## 🔍 **Debug Information**

Check browser console (F12) for these logs:

### Folder Selection
```
Selected folder with X files: [file names]
✅ Found file: 1.jpeg -> 1.jpeg
✅ Found file: 2.jpeg -> 2.jpeg
```

### Queue Processing
```
Processing queue with multi files support...
Processing multi field for item: 170006
Processing local files from selected folder: 2 files
Processing local file 1/2: 1.jpeg
✅ Added local file: 1.jpeg -> 1.jpeg
```

## ❌ **Troubleshooting**

### "No folder selected" Error
- Make sure you clicked "📁 Choose Folder" button
- Select a folder containing your files
- Check that files exist in the folder

### Files Not Found
- Verify file names match exactly (case-sensitive)
- Check file extensions are correct
- Ensure files are in the root of selected folder

### Captions Not Correct
- Captions now come from `multi` field items
- Each file gets its own caption from the API
- Main message caption is ignored for multi-items

### Files Not Attaching
- Check file sizes (must be under 16MB)
- Verify file formats are supported
- Look for error messages in console

## 📝 **API Response Structure**

Your API should return:
```json
{
  "success": true,
  "whatsapp": [
    {
      "id": "170006",
      "mobile": "919703924689",
      "message": "main message (ignored for multi-items)",
      "caption": "main caption (ignored for multi-items)",
      "multi": "{\"success\":true,\"whatsapp\":[
        {
          \"message\":\"loopmessage1\",
          \"caption\":\"caption for first image\",
          \"link\":\"https://example.com/cloud-file.jpg\"
        },
        {
          \"message\":\"looplocalmessage1\",
          \"caption\":\"caption for local file\",
          \"link\":\"1.jpeg\"
        }
      ]}"
    }
  ]
}
```

## ✅ **Success Indicators**

1. **Folder Selected**: Input shows folder name
2. **Files Found**: Console shows "✅ Found file" messages
3. **Queue Populated**: Shows correct number of items with file types
4. **Captions Correct**: Each item shows caption from `multi` field
5. **Files Attached**: Queue shows file names and types

## 🎯 **Key Improvements**

- ✅ **No more individual file selection dialogs**
- ✅ **Automatic file matching by name**
- ✅ **Correct captions from multi field**
- ✅ **All files processed and attached**
- ✅ **Better error handling and logging**
- ✅ **Clear visual indicators for file types**

The system now works exactly as requested: select a folder once, and all files referenced in the `multi` field are automatically found and attached with their correct captions!
